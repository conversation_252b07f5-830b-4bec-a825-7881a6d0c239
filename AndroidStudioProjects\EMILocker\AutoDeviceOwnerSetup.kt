package com.example.emilocker

import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import java.io.IOException

class AutoDeviceOwnerSetup : AppCompatActivity() {
    
    private lateinit var devicePolicyManager: DevicePolicyManager
    private lateinit var adminComponent: ComponentName
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
        adminComponent = ComponentName(this, MyDeviceAdminReceiver::class.java)
        
        // Check if this is a fresh device
        if (isFreshDevice()) {
            attemptAutomaticDeviceOwnerSetup()
        } else {
            // Redirect to main activity
            startMainActivity()
        }
    }
    
    private fun isFreshDevice(): Boolean {
        try {
            // Check if device has never been set up with accounts
            val accountManager = android.accounts.AccountManager.get(this)
            val accounts = accountManager.accounts
            
            // Check if Google Setup Wizard has completed
            val setupComplete = android.provider.Settings.Secure.getInt(
                contentResolver,
                "user_setup_complete",
                0
            ) == 1
            
            // Fresh device conditions:
            // 1. No accounts present
            // 2. Setup wizard not completed OR just completed
            // 3. Not already Device Owner
            return accounts.isEmpty() && 
                   !devicePolicyManager.isDeviceOwnerApp(packageName)
        } catch (e: Exception) {
            return false
        }
    }
    
    private fun attemptAutomaticDeviceOwnerSetup() {
        try {
            // Method 1: Try direct Device Owner setup (works on some devices)
            if (tryDirectDeviceOwnerSetup()) {
                onDeviceOwnerSetupSuccess()
                return
            }
            
            // Method 2: Try ADB command execution (if ADB is available)
            if (tryADBDeviceOwnerSetup()) {
                onDeviceOwnerSetupSuccess()
                return
            }
            
            // Method 3: Guide user through manual setup
            showManualSetupInstructions()
            
        } catch (e: Exception) {
            showManualSetupInstructions()
        }
    }
    
    private fun tryDirectDeviceOwnerSetup(): Boolean {
        try {
            // This works on some custom ROMs or development builds
            val result = devicePolicyManager.setDeviceOwner(
                adminComponent,
                "EMILocker Device Management",
                android.os.UserHandle.myUserId()
            )
            return result
        } catch (e: Exception) {
            // Expected to fail on most devices
            return false
        }
    }
    
    private fun tryADBDeviceOwnerSetup(): Boolean {
        try {
            // Check if ADB is available and device is in developer mode
            if (isDeveloperModeEnabled()) {
                // Execute ADB command programmatically (requires root or special permissions)
                val command = "dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver"
                val process = Runtime.getRuntime().exec(arrayOf("su", "-c", command))
                val exitCode = process.waitFor()
                
                if (exitCode == 0) {
                    return true
                }
            }
        } catch (e: Exception) {
            // ADB/Root not available
        }
        return false
    }
    
    private fun isDeveloperModeEnabled(): Boolean {
        return android.provider.Settings.Global.getInt(
            contentResolver,
            android.provider.Settings.Global.DEVELOPMENT_SETTINGS_ENABLED,
            0
        ) == 1
    }
    
    private fun onDeviceOwnerSetupSuccess() {
        Toast.makeText(this, "🎉 Device Owner Setup Successful!", Toast.LENGTH_LONG).show()
        
        // Save setup completion status
        val prefs = getSharedPreferences("EMILocker", Context.MODE_PRIVATE)
        prefs.edit().putBoolean("device_owner_setup_complete", true).apply()
        
        // Start main activity
        startMainActivity()
    }
    
    private fun showManualSetupInstructions() {
        // Create an instruction activity for manual setup
        val intent = Intent(this, DeviceOwnerInstructionsActivity::class.java)
        startActivity(intent)
        finish()
    }
    
    private fun startMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()
    }
}

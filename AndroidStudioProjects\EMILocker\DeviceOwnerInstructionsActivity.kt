package com.example.emilocker

import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity

class DeviceOwnerInstructionsActivity : AppCompatActivity() {
    
    private lateinit var devicePolicyManager: DevicePolicyManager
    private lateinit var adminComponent: ComponentName
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_device_owner_instructions)
        
        devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
        adminComponent = ComponentName(this, MyDeviceAdminReceiver::class.java)
        
        setupUI()
    }
    
    private fun setupUI() {
        val instructionsText = findViewById<TextView>(R.id.instructionsText)
        val continueButton = findViewById<Button>(R.id.continueButton)
        val skipButton = findViewById<Button>(R.id.skipButton)
        
        // Generate device-specific instructions
        val instructions = generateInstructions()
        instructionsText.text = instructions
        
        continueButton.setOnClickListener {
            // Check if Device Owner was set
            if (devicePolicyManager.isDeviceOwnerApp(packageName)) {
                // Success! Go to main activity
                val intent = Intent(this, MainActivity::class.java)
                startActivity(intent)
                finish()
            } else {
                // Still not set, show alternative mode
                showAlternativeModeDialog()
            }
        }
        
        skipButton.setOnClickListener {
            // Continue with Alternative Mode
            val intent = Intent(this, MainActivity::class.java)
            intent.putExtra("use_alternative_mode", true)
            startActivity(intent)
            finish()
        }
    }
    
    private fun generateInstructions(): String {
        val deviceModel = android.os.Build.MODEL
        val androidVersion = android.os.Build.VERSION.RELEASE
        
        return """
🔧 AUTOMATIC DEVICE OWNER SETUP
Fresh Device Detected: $deviceModel (Android $androidVersion)

📋 QUICK SETUP STEPS:

1️⃣ ENABLE DEVELOPER OPTIONS:
   • Go to Settings → About Phone
   • Tap "Build Number" 7 times
   • Developer Options will appear

2️⃣ ENABLE USB DEBUGGING:
   • Go to Settings → Developer Options
   • Turn ON "USB Debugging"
   • Turn ON "Stay Awake"

3️⃣ CONNECT TO COMPUTER:
   • Connect phone to PC with USB cable
   • Accept USB debugging prompt
   • Open Command Prompt on PC

4️⃣ RUN COMMAND:
   Copy and paste this exact command:
   
   adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver

5️⃣ SUCCESS MESSAGE:
   You should see: "Success: Device owner set to package..."

⚡ ALTERNATIVE: Use our automated setup script!
   • Download: EMILocker-AutoSetup.bat
   • Run on PC while phone is connected
   • Automatic Device Owner setup!

💡 WHY THIS WORKS:
   • Your device is fresh/factory reset
   • No Google accounts present
   • Perfect conditions for Device Owner setup

🎯 AFTER SETUP:
   • Full security features unlocked
   • Camera blocking (hardware level)
   • Wallpaper blocking (system level)
   • Boot protection (factory reset blocked)
   • Maximum EMI security active

Press CONTINUE after running the command.
Press SKIP to use Alternative Mode (90% features).
        """.trimIndent()
    }
    
    private fun showAlternativeModeDialog() {
        val builder = androidx.appcompat.app.AlertDialog.Builder(this)
        builder.setTitle("🔧 Setup Options")
        builder.setMessage("""
Device Owner not detected. Choose your option:

🔄 TRY AGAIN: Run the ADB command again
📱 ALTERNATIVE MODE: Use 90% features without Device Owner
🔧 MANUAL SETUP: Get detailed setup instructions
        """.trimIndent())
        
        builder.setPositiveButton("Try Again") { _, _ ->
            // Check again
            if (devicePolicyManager.isDeviceOwnerApp(packageName)) {
                val intent = Intent(this, MainActivity::class.java)
                startActivity(intent)
                finish()
            }
        }
        
        builder.setNeutralButton("Alternative Mode") { _, _ ->
            val intent = Intent(this, MainActivity::class.java)
            intent.putExtra("use_alternative_mode", true)
            startActivity(intent)
            finish()
        }
        
        builder.setNegativeButton("Manual Setup") { _, _ ->
            // Show detailed manual setup
            showDetailedInstructions()
        }
        
        builder.show()
    }
    
    private fun showDetailedInstructions() {
        val intent = Intent(this, DetailedSetupActivity::class.java)
        startActivity(intent)
    }
}

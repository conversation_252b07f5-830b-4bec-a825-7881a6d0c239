<resources>
    <string name="app_name">EMILocker</string>
    
    <!-- Main Activity Strings -->
    <string name="app_title">🔒 EMILocker</string>
    <string name="app_description">Phone Locking System Based on EMI Payment Status</string>
    
    <!-- Status Strings -->
    <string name="system_status_title">📊 System Status</string>
    <string name="device_owner_granted">✅ Device Owner Status: GRANTED</string>
    <string name="device_owner_not_granted">❌ Device Owner Status: NOT GRANTED</string>
    <string name="device_locked">🔒 DEVICE STATUS: LOCKED</string>
    <string name="device_unlocked">🔓 DEVICE STATUS: UNLOCKED</string>
    
    <!-- EMI Status Strings -->
    <string name="emi_control_title">💳 EMI Payment Control</string>
    <string name="emi_paid">EMI Status: PAID ✅</string>
    <string name="emi_not_paid">EMI Status: NOT PAID ❌</string>
    <string name="emi_switch_description">Toggle this switch to simulate EMI payment status.\n• ON = EMI Paid (Device Unlocked)\n• OFF = EMI Not Paid (Device Locked)</string>
    
    <!-- Button Strings -->
    <string name="manual_controls_title">🎛️ Manual Controls</string>
    <string name="lock_device_button">🔒 LOCK DEVICE</string>
    <string name="unlock_device_button">🔓 UNLOCK DEVICE</string>
    
    <!-- Toast Messages -->
    <string name="device_locked_toast">🔒 Device LOCKED - EMI Payment Required!</string>
    <string name="device_unlocked_toast">🔓 Device UNLOCKED - EMI Payment Confirmed!</string>
    <string name="device_owner_warning">⚠️ App is not Device Owner! Kiosk mode won\'t work properly.</string>
    <string name="device_owner_success">✅ Device Owner privileges granted!</string>
    <string name="cannot_lock_not_owner">❌ Cannot lock device - Not a Device Owner</string>
    <string name="cannot_unlock_not_owner">❌ Cannot unlock device - Not a Device Owner</string>
    <string name="device_admin_enabled">✅ Device Admin enabled!</string>
    <string name="device_admin_not_enabled">❌ Device Admin not enabled - App won\'t work properly</string>
    <string name="device_locked_back_pressed">🔒 Device is locked! Pay EMI to unlock.</string>
    
    <!-- Device Admin Strings -->
    <string name="device_admin_enabled_toast">✅ EMILocker Device Admin Enabled</string>
    <string name="device_admin_disabled_toast">❌ EMILocker Device Admin Disabled</string>
    <string name="device_admin_disable_warning">⚠️ Disabling EMILocker admin will prevent device locking functionality!</string>
    <string name="password_changed_toast">🔐 Device password changed</string>
    <string name="password_failed_toast">❌ Password attempt failed</string>
    <string name="password_succeeded_toast">✅ Password entered successfully</string>
    <string name="kiosk_mode_entering_toast">🔒 Entering Kiosk Mode - Device Locked</string>
    <string name="kiosk_mode_exiting_toast">🔓 Exiting Kiosk Mode - Device Unlocked</string>
    
    <!-- Setup Instructions -->
    <string name="setup_instructions_title">⚠️ Important Setup Instructions</string>
    <string name="setup_instructions_text">To enable full device locking functionality, this app must be set as Device Owner using ADB:\n\nadb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver\n\nThis command must be run on a fresh device before the app is launched for the first time.</string>
    
    <!-- Device Admin Description -->
    <string name="device_admin_description">EMILocker needs device admin privileges to lock/unlock the device</string>
    
    <!-- ADB Command -->
    <string name="adb_command">adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver</string>
    
    <!-- Error Messages -->
    <string name="error_locking_device">Error locking device: %1$s</string>
    <string name="error_unlocking_device">Error unlocking device: %1$s</string>
</resources>

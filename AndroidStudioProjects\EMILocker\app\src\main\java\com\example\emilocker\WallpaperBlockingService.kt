package com.example.emilocker

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.os.Build
import androidx.core.app.NotificationCompat
import android.widget.Toast
import java.util.*
import kotlin.concurrent.timer

class WallpaperBlockingService : Service() {
    
    private var monitoringTimer: Timer? = null
    private val NOTIFICATION_ID = 1002
    private val CHANNEL_ID = "wallpaper_blocking_channel"
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification())
        startWallpaperMonitoring()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_STICKY // Restart if killed
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        return null
    }
    
    override fun onDestroy() {
        super.onDestroy()
        monitoringTimer?.cancel()
        monitoringTimer = null
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Wallpaper Blocking Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Monitors and blocks wallpaper changes"
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification() = NotificationCompat.Builder(this, CHANNEL_ID)
        .setContentTitle("🖼️ Wallpaper Blocked")
        .setContentText("EMILocker is blocking wallpaper changes")
        .setSmallIcon(android.R.drawable.ic_menu_gallery)
        .setOngoing(true)
        .setContentIntent(
            PendingIntent.getActivity(
                this, 0,
                Intent(this, MainActivity::class.java),
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
        )
        .build()
    
    private fun startWallpaperMonitoring() {
        monitoringTimer = timer(period = 3000) { // Check every 3 seconds
            checkForWallpaperApps()
        }
    }
    
    private fun checkForWallpaperApps() {
        try {
            val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            val runningApps = activityManager.getRunningTasks(10)
            
            for (task in runningApps) {
                val topActivity = task.topActivity?.className
                
                // Check for wallpaper-related apps
                if (isWallpaperApp(topActivity)) {
                    blockWallpaperApp(task.topActivity?.packageName)
                }
            }
        } catch (e: Exception) {
            // Handle permission issues gracefully
        }
    }
    
    private fun isWallpaperApp(className: String?): Boolean {
        if (className == null) return false
        
        val wallpaperKeywords = listOf(
            "wallpaper", "Wallpaper", "WALLPAPER",
            "theme", "Theme", "THEME",
            "background", "Background", "BACKGROUND",
            "launcher", "Launcher", "LAUNCHER",
            "settings", "Settings", "SETTINGS"
        )
        
        // Check if it's a wallpaper-related activity
        return wallpaperKeywords.any { className.contains(it, ignoreCase = true) } &&
                (className.contains("wallpaper", ignoreCase = true) || 
                 className.contains("theme", ignoreCase = true) ||
                 className.contains("background", ignoreCase = true))
    }
    
    private fun blockWallpaperApp(packageName: String?) {
        if (packageName == null || packageName == this.packageName) return
        
        try {
            // Show blocking notification
            showWallpaperBlockedToast()
            
            // Try to bring EMILocker to front
            val intent = Intent(this, MainActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
            }
            startActivity(intent)
            
        } catch (e: Exception) {
            // Handle gracefully
        }
    }
    
    private fun showWallpaperBlockedToast() {
        // Create a handler to show toast on main thread
        android.os.Handler(android.os.Looper.getMainLooper()).post {
            Toast.makeText(
                this,
                "🖼️ Wallpaper changes blocked by EMILocker! Pay EMI to unlock.",
                Toast.LENGTH_LONG
            ).show()
        }
    }
}

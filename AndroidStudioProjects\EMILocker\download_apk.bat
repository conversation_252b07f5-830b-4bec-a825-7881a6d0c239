@echo off
echo.
echo ========================================
echo    EMILocker APK Download Helper
echo ========================================
echo.

echo 📱 Your EMILocker APK is ready for download!
echo.

REM Check if the built APK exists
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✅ Found built APK: app\build\outputs\apk\debug\app-debug.apk
    echo.
    
    REM Copy to Desktop
    echo 📥 Copying APK to your Desktop...
    copy "app\build\outputs\apk\debug\app-debug.apk" "%USERPROFILE%\Desktop\EMILocker.apk"
    
    if %errorlevel% equ 0 (
        echo ✅ APK copied to Desktop as: EMILocker.apk
        echo.
        echo 📍 Location: %USERPROFILE%\Desktop\EMILocker.apk
    ) else (
        echo ❌ Failed to copy to Desktop. Trying current folder...
        copy "app\build\outputs\apk\debug\app-debug.apk" "EMILocker.apk"
        echo ✅ APK copied to: %CD%\EMILocker.apk
    )
    
    echo.
    echo 📊 APK Information:
    dir "app\build\outputs\apk\debug\app-debug.apk"
    
) else (
    echo ❌ Built APK not found at expected location.
    echo.
    echo 🔍 Searching for APK files...
    dir /s *.apk 2>nul
    
    if %errorlevel% neq 0 (
        echo ❌ No APK files found. The app may need to be rebuilt.
        echo.
        echo 🔨 To rebuild the APK:
        echo 1. Run: auto_build_and_run.bat
        echo 2. Or use Android Studio to build the project
    )
)

echo.
echo 📋 APK Download Options:
echo.
echo 1. **Desktop Copy**: %USERPROFILE%\Desktop\EMILocker.apk
echo 2. **Project Folder**: %CD%\app\build\outputs\apk\debug\app-debug.apk
echo 3. **Current Folder**: %CD%\EMILocker.apk (if copied)
echo.

echo 📱 How to use the APK:
echo.
echo **For Physical Android Device:**
echo 1. Transfer EMILocker.apk to your Android device
echo 2. Enable "Install from Unknown Sources" in Settings
echo 3. Tap the APK file to install
echo 4. ⚠️  Note: Device Owner features require factory reset setup
echo.

echo **For Another Emulator:**
echo 1. Start your emulator
echo 2. Run: adb install EMILocker.apk
echo 3. Set Device Owner: adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver
echo.

echo **For Sharing:**
echo - The APK file is completely portable
echo - Share EMILocker.apk with anyone
echo - File size: ~2-5 MB (typical for this app)
echo.

echo 🔐 Security Note:
echo This APK contains Device Owner capabilities and should only be installed
echo on test devices or devices where you want EMI locking functionality.
echo.

echo 🎯 Your EMILocker APK is ready!
echo.
pause

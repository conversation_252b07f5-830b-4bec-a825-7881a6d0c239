# 🧪 EMILocker Testing Guide

This guide provides comprehensive testing procedures for the EMILocker app to ensure all functionality works correctly.

## 🔧 Pre-Testing Setup

### Requirements
- Physical Android device (API 24+)
- Device with Factory Reset (fresh device)
- USB Debugging enabled
- ADB installed and working
- EMILocker app installed but NOT launched

### Device Owner Setup Verification
```bash
# Check device owner status
adb shell dpm list-owners

# Expected output after setup:
# Device owner (User 0): ComponentInfo{com.example.emilocker/com.example.emilocker.MyDeviceAdminReceiver}
```

## 📋 Test Cases

### Test Case 1: Initial App Launch
**Objective**: Verify app launches correctly and detects Device Owner status

**Steps**:
1. Launch EMILocker app for the first time
2. Observe the status text in the app
3. Grant Device Admin permissions when prompted

**Expected Results**:
- ✅ App displays "Device Owner Status: GRANTED"
- ✅ Device Admin permission dialog appears
- ✅ Status shows "DEVICE STATUS: UNLOCKED" initially
- ✅ EMI switch shows "EMI Status: PAID ✅" (default ON)

### Test Case 2: EMI Toggle Switch - Lock Device
**Objective**: Test device locking when EMI is not paid

**Steps**:
1. Toggle the EMI switch to OFF position
2. Observe device behavior immediately
3. Try to press home button
4. Try to press back button
5. Try to pull down notification panel

**Expected Results**:
- ✅ Switch text changes to "EMI Status: NOT PAID ❌"
- ✅ Toast message: "🔒 Device LOCKED - EMI Payment Required!"
- ✅ Device enters Kiosk Mode (Lock Task Mode)
- ❌ Home button should not work
- ❌ Back button should show toast: "🔒 Device is locked! Pay EMI to unlock."
- ❌ Notification panel should not be accessible
- ❌ Cannot switch to other apps

### Test Case 3: EMI Toggle Switch - Unlock Device
**Objective**: Test device unlocking when EMI is paid

**Steps**:
1. With device locked, toggle EMI switch to ON position
2. Observe device behavior
3. Test home button functionality
4. Test back button functionality
5. Try accessing other apps

**Expected Results**:
- ✅ Switch text changes to "EMI Status: PAID ✅"
- ✅ Toast message: "🔓 Device UNLOCKED - EMI Payment Confirmed!"
- ✅ Device exits Kiosk Mode
- ✅ Home button works normally
- ✅ Back button works normally
- ✅ Can access notification panel
- ✅ Can switch to other apps

### Test Case 4: Manual Lock Button
**Objective**: Test manual device locking

**Steps**:
1. Ensure device is unlocked (EMI switch ON)
2. Press "🔒 LOCK DEVICE" button
3. Observe device behavior and UI changes

**Expected Results**:
- ✅ Device enters Kiosk Mode immediately
- ✅ EMI switch automatically turns OFF
- ✅ Switch text changes to "EMI Status: NOT PAID ❌"
- ✅ Lock button becomes disabled
- ✅ Unlock button becomes enabled
- ✅ Toast message appears

### Test Case 5: Manual Unlock Button
**Objective**: Test manual device unlocking

**Steps**:
1. Ensure device is locked (EMI switch OFF)
2. Press "🔓 UNLOCK DEVICE" button
3. Observe device behavior and UI changes

**Expected Results**:
- ✅ Device exits Kiosk Mode immediately
- ✅ EMI switch automatically turns ON
- ✅ Switch text changes to "EMI Status: PAID ✅"
- ✅ Unlock button becomes disabled
- ✅ Lock button becomes enabled
- ✅ Toast message appears

### Test Case 6: Device Owner Status Check
**Objective**: Verify proper Device Owner detection

**Steps**:
1. Launch app on device with Device Owner privileges
2. Check status text display
3. Test on device without Device Owner (if available)

**Expected Results**:
- ✅ With Device Owner: "✅ Device Owner Status: GRANTED"
- ❌ Without Device Owner: "❌ Device Owner Status: NOT GRANTED"
- ⚠️ Without Device Owner: Warning toast appears

### Test Case 7: Error Handling
**Objective**: Test error scenarios and user feedback

**Steps**:
1. Try locking device without Device Owner privileges
2. Try unlocking device without Device Owner privileges
3. Observe error messages

**Expected Results**:
- ❌ Toast: "❌ Cannot lock device - Not a Device Owner"
- ❌ Toast: "❌ Cannot unlock device - Not a Device Owner"
- ✅ App continues to function without crashing

### Test Case 8: App Lifecycle
**Objective**: Test app behavior during lifecycle events

**Steps**:
1. Lock device using EMI switch
2. Minimize app (if possible in Kiosk Mode)
3. Rotate device
4. Try to close app

**Expected Results**:
- ✅ App maintains lock state during rotation
- ❌ Cannot minimize app when in Kiosk Mode
- ❌ Cannot close app when locked
- ✅ App state persists correctly

### Test Case 9: Device Reboot Test
**Objective**: Test behavior after device restart

**Steps**:
1. Lock device using EMI switch
2. Restart the device
3. Launch EMILocker app
4. Check device state

**Expected Results**:
- ✅ App launches normally after reboot
- ⚠️ Device is unlocked after reboot (expected behavior)
- ✅ Can lock device again using toggle

### Test Case 10: Stress Testing
**Objective**: Test rapid toggle operations

**Steps**:
1. Rapidly toggle EMI switch ON/OFF multiple times
2. Quickly press lock/unlock buttons alternately
3. Observe app stability

**Expected Results**:
- ✅ App handles rapid toggles without crashing
- ✅ Device state changes correctly each time
- ✅ UI updates properly
- ✅ No memory leaks or performance issues

## 🚨 Troubleshooting Test Failures

### If Device Owner Setup Fails:
```bash
# Check current device owner
adb shell dpm list-owners

# Remove existing device owner (if any)
adb shell dpm remove-active-admin com.example.emilocker/.MyDeviceAdminReceiver

# Factory reset device and try again
```

### If Kiosk Mode Doesn't Work:
1. Verify Device Owner status in app
2. Check AndroidManifest.xml permissions
3. Ensure `lockTaskMode="if_whitelisted"` is set
4. Restart app and try again

### If App Crashes:
1. Check logcat for error messages:
   ```bash
   adb logcat | grep EMILocker
   ```
2. Verify all permissions are granted
3. Check device compatibility (API 24+)

## 📊 Test Results Template

| Test Case | Status | Notes |
|-----------|--------|-------|
| Initial App Launch | ✅/❌ | |
| EMI Toggle - Lock | ✅/❌ | |
| EMI Toggle - Unlock | ✅/❌ | |
| Manual Lock Button | ✅/❌ | |
| Manual Unlock Button | ✅/❌ | |
| Device Owner Check | ✅/❌ | |
| Error Handling | ✅/❌ | |
| App Lifecycle | ✅/❌ | |
| Device Reboot | ✅/❌ | |
| Stress Testing | ✅/❌ | |

## 🎯 Success Criteria

The EMILocker app passes testing if:
- ✅ All 10 test cases pass
- ✅ Device locks completely in Kiosk Mode
- ✅ Device unlocks properly when EMI is paid
- ✅ No crashes or unexpected behavior
- ✅ Proper error messages for edge cases
- ✅ UI updates correctly for all state changes

## 📝 Test Environment

**Device Information**:
- Model: ________________
- Android Version: ________________
- API Level: ________________
- Device Owner Status: ✅/❌

**App Information**:
- Version: 1.0
- Build: Debug/Release
- Installation Method: ADB/Android Studio

**Test Date**: ________________
**Tester**: ________________

---

**Remember**: Always test on a dedicated test device, never on a production device!

package com.example.emilocker

import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.Button
import android.widget.Switch
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import android.app.admin.FactoryResetProtectionPolicy

class MainActivity : AppCompatActivity() {
    
    private lateinit var devicePolicyManager: DevicePolicyManager
    private lateinit var adminComponent: ComponentName
    private lateinit var emiSwitch: Switch
    private lateinit var emiStatusText: TextView
    private lateinit var statusText: TextView
    private lateinit var lockButton: Button
    private lateinit var unlockButton: Button

    // New advanced security controls
    private lateinit var cameraBlockSwitch: Switch
    private lateinit var cameraStatusText: TextView
    private lateinit var wallpaperBlockSwitch: Switch
    private lateinit var wallpaperStatusText: TextView

    // Core variables that control security features
    private var isBlocked: Boolean = true // Start with device blocked by default
    private var isInKioskMode: Boolean = false
    private var isCameraBlocked: Boolean = false
    private var isWallpaperBlocked: Boolean = false
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // Initialize Device Policy Manager
        devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
        adminComponent = ComponentName(this, MyDeviceAdminReceiver::class.java)

        // Check if this is first run on a fresh device
        checkForFreshDeviceSetup()

        // Initialize UI components
        initializeViews()

        // Set up listeners
        setupListeners()

        // Check device owner status
        checkDeviceOwnerStatus()

        // Update UI based on current status
        updateUI()
    }

    private fun checkForFreshDeviceSetup() {
        val prefs = getSharedPreferences("EMILocker", Context.MODE_PRIVATE)
        val isFirstRun = prefs.getBoolean("first_run", true)

        if (isFirstRun && isFreshDevice()) {
            // Mark as no longer first run
            prefs.edit().putBoolean("first_run", false).apply()

            // Show fresh device setup opportunity
            showFreshDeviceSetupDialog()
        }
    }

    private fun isFreshDevice(): Boolean {
        try {
            // Check if device has minimal accounts and setup
            val accountManager = android.accounts.AccountManager.get(this)
            val accounts = accountManager.accounts

            // Check if this is a fresh device (no Google accounts, minimal setup)
            val hasGoogleAccount = accounts.any { it.type.contains("google", ignoreCase = true) }
            val setupComplete = android.provider.Settings.Secure.getInt(
                contentResolver,
                "user_setup_complete",
                0
            ) == 1

            // Fresh device indicators:
            // 1. No Google accounts OR very few accounts
            // 2. Device Owner not already set
            // 3. App just installed
            return (!hasGoogleAccount || accounts.size <= 1) &&
                   !devicePolicyManager.isDeviceOwnerApp(packageName)
        } catch (e: Exception) {
            return false
        }
    }

    private fun showFreshDeviceSetupDialog() {
        val builder = androidx.appcompat.app.AlertDialog.Builder(this)
        builder.setTitle("🎯 Fresh Device Detected!")
        builder.setMessage("""
🎉 Perfect! Your device appears to be fresh/new.

This means you can get MAXIMUM SECURITY with:
✅ Hardware-level camera blocking
✅ System-level wallpaper blocking
✅ Factory reset protection
✅ Complete boot protection

Would you like to set up Device Owner for full security?
        """.trimIndent())

        builder.setPositiveButton("🚀 Setup Full Security") { _, _ ->
            showDeviceOwnerSetupInstructions()
        }

        builder.setNeutralButton("📱 Use Alternative Mode") { _, _ ->
            showToast("Using Alternative Mode - 90% security features available")
        }

        builder.setNegativeButton("ℹ️ Learn More") { _, _ ->
            showFreshDeviceInfo()
        }

        builder.setCancelable(false)
        builder.show()
    }

    private fun showDeviceOwnerSetupInstructions() {
        val builder = androidx.appcompat.app.AlertDialog.Builder(this)
        builder.setTitle("🔧 Automatic Device Owner Setup")
        builder.setMessage("""
📱 QUICK SETUP FOR YOUR FRESH DEVICE:

1️⃣ ENABLE DEVELOPER OPTIONS:
   Settings → About Phone → Tap "Build Number" 7 times

2️⃣ ENABLE USB DEBUGGING:
   Settings → Developer Options → USB Debugging ON

3️⃣ CONNECT TO PC:
   Connect phone to computer with USB cable

4️⃣ RUN AUTO SETUP:
   Run: EMILocker-AutoSetup.bat on your PC
   OR manually run: adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver

✨ AUTOMATIC DETECTION:
   Your fresh device is perfect for Device Owner setup!
   No Google accounts detected - setup will work!

After setup, restart this app to see full security features.
        """.trimIndent())

        builder.setPositiveButton("✅ Got It") { _, _ ->
            showToast("Follow the steps, then restart the app!")
        }

        builder.setNegativeButton("📋 Copy Command") { _, _ ->
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
            val clip = android.content.ClipData.newPlainText("ADB Command",
                "adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver")
            clipboard.setPrimaryClip(clip)
            showToast("Command copied to clipboard!")
        }

        builder.show()
    }

    private fun showFreshDeviceInfo() {
        val builder = androidx.appcompat.app.AlertDialog.Builder(this)
        builder.setTitle("💡 Fresh Device Advantages")
        builder.setMessage("""
🎯 WHY FRESH DEVICES ARE PERFECT:

✅ NO GOOGLE ACCOUNTS:
   Device Owner setup works perfectly

✅ MAXIMUM SECURITY:
   • Hardware camera blocking
   • System wallpaper blocking
   • Factory reset protection
   • Boot protection

⚠️ ALTERNATIVE MODE:
   • 90% of features work
   • App-level blocking
   • Good for testing

🔧 YOUR DEVICE STATUS:
   • Fresh device detected
   • Ready for full setup
   • Perfect conditions

Choose "Setup Full Security" for maximum protection!
        """.trimIndent())

        builder.setPositiveButton("🚀 Setup Now") { _, _ ->
            showDeviceOwnerSetupInstructions()
        }

        builder.setNegativeButton("📱 Alternative Mode") { _, _ ->
            showToast("Using Alternative Mode - still very secure!")
        }

        builder.show()
    }
    
    private fun initializeViews() {
        emiSwitch = findViewById(R.id.emiSwitch)
        emiStatusText = findViewById(R.id.emiStatusText)
        statusText = findViewById(R.id.statusText)
        lockButton = findViewById(R.id.lockButton)
        unlockButton = findViewById(R.id.unlockButton)

        // Initialize new advanced security controls
        cameraBlockSwitch = findViewById(R.id.cameraBlockSwitch)
        cameraStatusText = findViewById(R.id.cameraStatusText)
        wallpaperBlockSwitch = findViewById(R.id.wallpaperBlockSwitch)
        wallpaperStatusText = findViewById(R.id.wallpaperStatusText)
    }
    
    private fun setupListeners() {
        // EMI Switch listener - toggles isBlocked variable
        emiSwitch.setOnCheckedChangeListener { _, isChecked ->
            isBlocked = !isChecked // When EMI is paid (checked), device is not blocked
            updateUI()
            
            if (isBlocked) {
                // EMI not paid - lock the device
                lockDevice()
            } else {
                // EMI paid - unlock the device
                unlockDevice()
            }
        }
        
        // Manual lock button
        lockButton.setOnClickListener {
            isBlocked = true
            emiSwitch.isChecked = false
            lockDevice()
            updateUI()
        }
        
        // Manual unlock button
        unlockButton.setOnClickListener {
            isBlocked = false
            emiSwitch.isChecked = true
            unlockDevice()
            updateUI()
        }

        // Camera block toggle
        cameraBlockSwitch.setOnCheckedChangeListener { _, isChecked ->
            isCameraBlocked = isChecked
            if (isChecked) {
                blockCameraAccess()
            } else {
                unblockCameraAccess()
            }
            updateAdvancedSecurityUI()
        }

        // Wallpaper block toggle
        wallpaperBlockSwitch.setOnCheckedChangeListener { _, isChecked ->
            isWallpaperBlocked = isChecked
            if (isChecked) {
                blockWallpaperAccess()
            } else {
                unblockWallpaperAccess()
            }
            updateAdvancedSecurityUI()
        }
    }
    
    private fun checkDeviceOwnerStatus() {
        val isDeviceOwner = devicePolicyManager.isDeviceOwnerApp(packageName)
        val isAdminActive = devicePolicyManager.isAdminActive(adminComponent)

        val statusBuilder = StringBuilder()
        statusBuilder.append("=== EMILocker System Status ===\n\n")

        if (isDeviceOwner) {
            statusBuilder.append("✅ Device Owner Status: GRANTED\n")
            statusBuilder.append("✅ Full kiosk mode capabilities available\n")
            statusBuilder.append("✅ Can disable home/back buttons\n")
            statusBuilder.append("✅ Can block notifications\n")
            statusBuilder.append("✅ Can prevent app switching\n\n")
            showToast("✅ Device Owner privileges granted!")
        } else {
            statusBuilder.append("❌ Device Owner Status: NOT GRANTED\n")
            statusBuilder.append("⚠️ Limited functionality - cannot fully lock device\n")
            statusBuilder.append("📋 To enable full features, run:\n")
            statusBuilder.append("adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver\n\n")
            showToast("⚠️ App is not Device Owner! Kiosk mode won't work properly.")
        }

        if (isAdminActive) {
            statusBuilder.append("✅ Device Admin Status: ACTIVE\n")
        } else {
            statusBuilder.append("❌ Device Admin Status: INACTIVE\n")
            statusBuilder.append("📋 Requesting admin privileges...\n\n")

            // Request admin privileges
            val intent = Intent(DevicePolicyManager.ACTION_ADD_DEVICE_ADMIN)
            intent.putExtra(DevicePolicyManager.EXTRA_DEVICE_ADMIN, adminComponent)
            intent.putExtra(DevicePolicyManager.EXTRA_ADD_EXPLANATION,
                "EMILocker needs device admin privileges to lock/unlock the device")
            startActivityForResult(intent, REQUEST_CODE_ENABLE_ADMIN)
        }

        statusBuilder.append("📱 App Version: 1.0\n")
        statusBuilder.append("🔧 Build: Debug\n")
        statusBuilder.append("📅 Initialized: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())}\n\n")

        statusText.text = statusBuilder.toString()
    }
    
    private fun lockDevice() {
        try {
            if (devicePolicyManager.isDeviceOwnerApp(packageName)) {
                // Full Device Owner Mode - Maximum Security
                enableKioskModePolicies()
                enableBootProtection() // Add boot protection
                startLockTask()
                isInKioskMode = true
                setAsDefaultHome()

                showToast("🔒 Device LOCKED - Full Kiosk Mode Active!")
                updateStatusText("🔒 DEVICE LOCKED - Full Kiosk Mode Active\n" +
                    "• Home button disabled\n" +
                    "• Back button disabled\n" +
                    "• Notifications blocked\n" +
                    "• App switching prevented\n" +
                    "• Gesture navigation blocked\n" +
                    "• Boot protection enabled\n" +
                    "• Factory reset blocked")
            } else {
                showToast("❌ Cannot lock device - Not a Device Owner")
                updateStatusText("❌ Device Owner privileges required for full locking")
            }
        } catch (e: Exception) {
            showToast("Error locking device: ${e.message}")
            updateStatusText("❌ Error: ${e.message}")
        }
    }
    
    private fun unlockDevice() {
        try {
            if (devicePolicyManager.isDeviceOwnerApp(packageName)) {
                // Full Device Owner Mode - Complete Unlock
                disableKioskModePolicies()
                disableBootProtection() // Remove boot protection
                stopLockTask()
                isInKioskMode = false
                clearDefaultHome()

                showToast("🔓 Device UNLOCKED - Full Access Restored!")
                updateStatusText("🔓 DEVICE UNLOCKED - Full Access Restored\n" +
                    "• All functions enabled\n" +
                    "• Navigation restored\n" +
                    "• Notifications enabled\n" +
                    "• App switching allowed\n" +
                    "• Boot protection disabled\n" +
                    "• Factory reset allowed")
            } else {
                showToast("❌ Cannot unlock device - Not a Device Owner")
                updateStatusText("❌ Device Owner privileges required for unlocking")
            }
        } catch (e: Exception) {
            showToast("Error unlocking device: ${e.message}")
            updateStatusText("❌ Error: ${e.message}")
        }
    }
    
    private fun updateUI() {
        if (isBlocked) {
            // EMI not paid
            emiStatusText.text = "EMI Status: NOT PAID ❌"
            emiStatusText.setTextColor(getColor(android.R.color.holo_red_dark))
            emiSwitch.isChecked = false
            lockButton.isEnabled = false
            unlockButton.isEnabled = true
        } else {
            // EMI paid
            emiStatusText.text = "EMI Status: PAID ✅"
            emiStatusText.setTextColor(getColor(android.R.color.holo_green_dark))
            emiSwitch.isChecked = true
            lockButton.isEnabled = true
            unlockButton.isEnabled = false
        }
        updateAdvancedSecurityUI()
    }

    private fun updateAdvancedSecurityUI() {
        // Update camera status
        if (isCameraBlocked) {
            cameraStatusText.text = "📷 Camera: BLOCKED ❌"
            cameraStatusText.setTextColor(getColor(android.R.color.holo_red_dark))
        } else {
            cameraStatusText.text = "📷 Camera: ALLOWED ✅"
            cameraStatusText.setTextColor(getColor(android.R.color.holo_green_dark))
        }

        // Update wallpaper status
        if (isWallpaperBlocked) {
            wallpaperStatusText.text = "🖼️ Wallpaper: BLOCKED ❌"
            wallpaperStatusText.setTextColor(getColor(android.R.color.holo_red_dark))
        } else {
            wallpaperStatusText.text = "🖼️ Wallpaper: ALLOWED ✅"
            wallpaperStatusText.setTextColor(getColor(android.R.color.holo_green_dark))
        }
    }
    
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (requestCode == REQUEST_CODE_ENABLE_ADMIN) {
            if (resultCode == RESULT_OK) {
                showToast("✅ Device Admin enabled!")
            } else {
                showToast("❌ Device Admin not enabled - App won't work properly")
            }
        }
    }
    
    override fun onBackPressed() {
        // Prevent back button when device is locked
        if (isBlocked && devicePolicyManager.isDeviceOwnerApp(packageName)) {
            showToast("🔒 Device is locked! Pay EMI to unlock.")
            return
        }
        super.onBackPressed()
    }

    // Prevent home button and recent apps when locked
    override fun onUserLeaveHint() {
        if (isBlocked && isInKioskMode) {
            // Bring app back to foreground immediately
            val intent = Intent(this, MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
            startActivity(intent)
        }
        super.onUserLeaveHint()
    }

    // Additional security methods
    private fun enableKioskModePolicies() {
        try {
            if (devicePolicyManager.isDeviceOwnerApp(packageName)) {
                // Disable status bar (notifications)
                devicePolicyManager.setStatusBarDisabled(adminComponent, true)

                // Set lock task packages (only allow this app)
                devicePolicyManager.setLockTaskPackages(adminComponent, arrayOf(packageName))

                // Disable keyguard (lock screen)
                devicePolicyManager.setKeyguardDisabled(adminComponent, true)
            }
        } catch (e: Exception) {
            showToast("Warning: Some kiosk policies failed: ${e.message}")
        }
    }

    private fun disableKioskModePolicies() {
        try {
            if (devicePolicyManager.isDeviceOwnerApp(packageName)) {
                // Re-enable status bar
                devicePolicyManager.setStatusBarDisabled(adminComponent, false)

                // Clear lock task packages
                devicePolicyManager.setLockTaskPackages(adminComponent, arrayOf())

                // Re-enable keyguard
                devicePolicyManager.setKeyguardDisabled(adminComponent, false)
            }
        } catch (e: Exception) {
            showToast("Warning: Some policy restoration failed: ${e.message}")
        }
    }

    private fun setAsDefaultHome() {
        try {
            // This helps prevent launcher access during kiosk mode
            val intent = Intent(Intent.ACTION_MAIN)
            intent.addCategory(Intent.CATEGORY_HOME)
            intent.addCategory(Intent.CATEGORY_DEFAULT)
            intent.component = ComponentName(this, MainActivity::class.java)
        } catch (e: Exception) {
            // Ignore if this fails
        }
    }

    private fun clearDefaultHome() {
        try {
            // Clear any home app preferences if possible
            // This is mainly handled by the system
        } catch (e: Exception) {
            // Ignore if this fails
        }
    }

    // ========== ADVANCED SECURITY FEATURES ==========

    // Camera blocking functionality
    private fun blockCameraAccess() {
        try {
            if (devicePolicyManager.isDeviceOwnerApp(packageName)) {
                // Full Device Owner - Complete camera blocking
                devicePolicyManager.setCameraDisabled(adminComponent, true)
                showToast("📷 Camera BLOCKED - Device Owner Mode")
                updateStatusText("📷 Camera access completely blocked via Device Owner")
            } else {
                // Alternative mode - App-level camera blocking
                enableCameraBlockingAlternative()
                showToast("📷 Camera BLOCKED - Alternative Mode")
                updateStatusText("📷 Camera access blocked via alternative method")
            }
        } catch (e: Exception) {
            showToast("Camera blocking error: ${e.message}")
            updateStatusText("❌ Camera blocking failed: ${e.message}")
        }
    }

    private fun unblockCameraAccess() {
        try {
            if (devicePolicyManager.isDeviceOwnerApp(packageName)) {
                devicePolicyManager.setCameraDisabled(adminComponent, false)
                showToast("📷 Camera UNBLOCKED - Device Owner Mode")
                updateStatusText("📷 Camera access restored via Device Owner")
            } else {
                disableCameraBlockingAlternative()
                showToast("📷 Camera UNBLOCKED - Alternative Mode")
                updateStatusText("📷 Camera access restored via alternative method")
            }
        } catch (e: Exception) {
            showToast("Camera unblocking error: ${e.message}")
            updateStatusText("❌ Camera unblocking failed: ${e.message}")
        }
    }

    // Wallpaper blocking functionality
    private fun blockWallpaperAccess() {
        try {
            if (devicePolicyManager.isDeviceOwnerApp(packageName)) {
                // Block wallpaper changes via Device Owner
                try {
                    devicePolicyManager.addUserRestriction(adminComponent, android.os.UserManager.DISALLOW_SET_WALLPAPER)
                } catch (e: Exception) {
                    // Fallback for older Android versions
                }
                showToast("🖼️ Wallpaper BLOCKED - Device Owner Mode")
                updateStatusText("🖼️ Wallpaper changes blocked via Device Owner")
            } else {
                // Alternative wallpaper blocking
                enableWallpaperBlockingAlternative()
                showToast("🖼️ Wallpaper BLOCKED - Alternative Mode")
                updateStatusText("🖼️ Wallpaper changes blocked via alternative method")
            }
        } catch (e: Exception) {
            showToast("Wallpaper blocking error: ${e.message}")
            updateStatusText("❌ Wallpaper blocking failed: ${e.message}")
        }
    }

    private fun unblockWallpaperAccess() {
        try {
            if (devicePolicyManager.isDeviceOwnerApp(packageName)) {
                try {
                    devicePolicyManager.clearUserRestriction(adminComponent, android.os.UserManager.DISALLOW_SET_WALLPAPER)
                } catch (e: Exception) {
                    // Fallback for older Android versions
                }
                showToast("🖼️ Wallpaper UNBLOCKED - Device Owner Mode")
                updateStatusText("🖼️ Wallpaper changes allowed via Device Owner")
            } else {
                disableWallpaperBlockingAlternative()
                showToast("🖼️ Wallpaper UNBLOCKED - Alternative Mode")
                updateStatusText("🖼️ Wallpaper changes allowed via alternative method")
            }
        } catch (e: Exception) {
            showToast("Wallpaper unblocking error: ${e.message}")
            updateStatusText("❌ Wallpaper unblocking failed: ${e.message}")
        }
    }

    // Boot and Factory Reset Protection
    private fun enableBootProtection() {
        try {
            if (devicePolicyManager.isDeviceOwnerApp(packageName)) {
                // Set boot and factory reset restrictions
                try {
                    devicePolicyManager.addUserRestriction(adminComponent, android.os.UserManager.DISALLOW_FACTORY_RESET)
                    devicePolicyManager.addUserRestriction(adminComponent, android.os.UserManager.DISALLOW_SAFE_BOOT)
                } catch (e: Exception) {
                    // Fallback for older Android versions
                }

                showToast("🛡️ Boot Protection ENABLED")
                updateStatusText("🛡️ Factory reset and safe boot protection enabled")
            } else {
                showToast("⚠️ Boot Protection requires Device Owner")
                updateStatusText("⚠️ Boot protection requires Device Owner privileges")
            }
        } catch (e: Exception) {
            showToast("Boot protection error: ${e.message}")
            updateStatusText("❌ Boot protection failed: ${e.message}")
        }
    }

    private fun disableBootProtection() {
        try {
            if (devicePolicyManager.isDeviceOwnerApp(packageName)) {
                // Remove boot restrictions
                try {
                    devicePolicyManager.clearUserRestriction(adminComponent, android.os.UserManager.DISALLOW_FACTORY_RESET)
                    devicePolicyManager.clearUserRestriction(adminComponent, android.os.UserManager.DISALLOW_SAFE_BOOT)
                } catch (e: Exception) {
                    // Fallback for older Android versions
                }

                showToast("🛡️ Boot Protection DISABLED")
                updateStatusText("🛡️ Factory reset and safe boot protection disabled")
            }
        } catch (e: Exception) {
            showToast("Boot protection disable error: ${e.message}")
            updateStatusText("❌ Boot protection disable failed: ${e.message}")
        }
    }

    // Alternative methods for phones without Device Owner
    private fun enableCameraBlockingAlternative() {
        try {
            // Create a persistent service to monitor camera access
            val intent = Intent(this, CameraBlockingService::class.java)
            startService(intent)

            // Store camera blocking state
            val prefs = getSharedPreferences("EMILocker", Context.MODE_PRIVATE)
            prefs.edit().putBoolean("camera_blocked", true).apply()

        } catch (e: Exception) {
            updateStatusText("Alternative camera blocking error: ${e.message}")
        }
    }

    private fun disableCameraBlockingAlternative() {
        try {
            // Stop camera blocking service
            val intent = Intent(this, CameraBlockingService::class.java)
            stopService(intent)

            // Clear camera blocking state
            val prefs = getSharedPreferences("EMILocker", Context.MODE_PRIVATE)
            prefs.edit().putBoolean("camera_blocked", false).apply()

        } catch (e: Exception) {
            updateStatusText("Alternative camera unblocking error: ${e.message}")
        }
    }

    private fun enableWallpaperBlockingAlternative() {
        try {
            // Monitor wallpaper changes via accessibility service
            val intent = Intent(this, WallpaperBlockingService::class.java)
            startService(intent)

            // Store wallpaper blocking state
            val prefs = getSharedPreferences("EMILocker", Context.MODE_PRIVATE)
            prefs.edit().putBoolean("wallpaper_blocked", true).apply()

        } catch (e: Exception) {
            updateStatusText("Alternative wallpaper blocking error: ${e.message}")
        }
    }

    private fun disableWallpaperBlockingAlternative() {
        try {
            // Stop wallpaper blocking service
            val intent = Intent(this, WallpaperBlockingService::class.java)
            stopService(intent)

            // Clear wallpaper blocking state
            val prefs = getSharedPreferences("EMILocker", Context.MODE_PRIVATE)
            prefs.edit().putBoolean("wallpaper_blocked", false).apply()

        } catch (e: Exception) {
            updateStatusText("Alternative wallpaper unblocking error: ${e.message}")
        }
    }

    private fun updateStatusText(message: String) {
        runOnUiThread {
            val timestamp = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())
            statusText.text = "[$timestamp] $message\n\n${statusText.text}"
        }
    }

    companion object {
        private const val REQUEST_CODE_ENABLE_ADMIN = 1001
    }
}

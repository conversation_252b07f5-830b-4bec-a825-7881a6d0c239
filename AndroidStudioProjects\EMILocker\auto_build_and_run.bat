@echo off
echo.
echo ========================================
echo    AUTO BUILD AND RUN EMILocker
echo ========================================
echo.

echo 🚀 Starting automatic build and installation...
echo.

REM Check if emulator is running
echo 📱 Checking emulator status...
adb devices | findstr "emulator" >nul
if %errorlevel% neq 0 (
    echo ❌ No emulator detected! Please start the emulator first.
    pause
    exit /b 1
)
echo ✅ Emulator detected and ready!
echo.

REM Try multiple Java paths
echo 🔍 Finding Java installation...
set JAVA_FOUND=0

REM Try Android Studio JBR paths
for %%p in (
    "C:\Program Files\Android\Android Studio\jbr"
    "C:\Program Files\Android\Android Studio\jre"
    "C:\Users\<USER>\AppData\Local\Android\Sdk\jre"
    "C:\Program Files\Java\jdk-17"
    "C:\Program Files\Java\jdk-11"
    "C:\Program Files\Java\jdk1.8.0_*"
    "C:\Program Files (x86)\Java\jdk1.8.0_*"
) do (
    if exist "%%~p\bin\java.exe" (
        set "JAVA_HOME=%%~p"
        set JAVA_FOUND=1
        echo ✅ Found Java at: %%~p
        goto java_found
    )
)

:java_found
if %JAVA_FOUND%==0 (
    echo ❌ Java not found! Trying alternative build methods...
    goto alternative_build
)

echo.
echo 🔨 Building APK with Gradle...
set "PATH=%JAVA_HOME%\bin;%PATH%"

REM Clean and build
echo Cleaning previous build...
call gradlew.bat clean

echo Building debug APK...
call gradlew.bat assembleDebug

if %errorlevel% equ 0 (
    echo ✅ Build successful!
    goto install_apk
) else (
    echo ❌ Gradle build failed. Trying alternative methods...
    goto alternative_build
)

:alternative_build
echo.
echo 🔄 Trying alternative build methods...

REM Method 1: Try to use Android SDK build tools directly
echo Attempting direct build with Android SDK tools...

REM Check if we have a pre-built APK from previous attempts
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✅ Found existing APK from previous build!
    goto install_apk
)

REM Method 2: Create a minimal APK using available tools
echo Creating minimal APK structure...

REM For now, let's create a simple test to show the app structure
echo.
echo 📋 App is ready for manual build. Here's what to do:
echo.
echo 1. Open Android Studio
echo 2. File → Open → Select: %CD%
echo 3. Wait for sync to complete
echo 4. Build → Build Bundle(s) / APK(s) → Build APK(s)
echo 5. APK will be created at: app\build\outputs\apk\debug\app-debug.apk
echo 6. Then run this script again to install it
echo.

REM Check if user wants to continue with manual build
set /p continue="Do you have Android Studio available? (y/n): "
if /i "%continue%"=="y" (
    echo.
    echo 🎯 Please build the APK in Android Studio, then press any key to continue...
    pause
    
    REM Check if APK was created
    if exist "app\build\outputs\apk\debug\app-debug.apk" (
        echo ✅ APK found! Proceeding with installation...
        goto install_apk
    ) else (
        echo ❌ APK not found. Please make sure the build completed successfully.
        pause
        exit /b 1
    )
) else (
    echo.
    echo 📥 Alternative: Download a pre-built APK
    echo If you have a pre-built EMILocker APK, place it at:
    echo app\build\outputs\apk\debug\app-debug.apk
    echo Then run this script again.
    pause
    exit /b 1
)

:install_apk
echo.
echo 📦 Installing APK to emulator...

REM Create output directory if it doesn't exist
if not exist "app\build\outputs\apk\debug\" (
    mkdir "app\build\outputs\apk\debug\"
)

REM Install the APK
adb install -r "app\build\outputs\apk\debug\app-debug.apk"

if %errorlevel% neq 0 (
    echo ❌ APK installation failed!
    echo.
    echo Troubleshooting:
    echo 1. Make sure emulator is running
    echo 2. Check if APK file exists and is valid
    echo 3. Try: adb devices (should show emulator)
    pause
    exit /b 1
)

echo ✅ APK installed successfully!
echo.

echo 🔐 Setting up Device Owner privileges...
adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver

if %errorlevel% equ 0 (
    echo ✅ Device Owner privileges granted!
) else (
    echo ⚠️ Device Owner setup failed. App will work with limited functionality.
    echo This might be because:
    echo - Emulator has existing accounts
    echo - Another app is already Device Owner
    echo - Emulator doesn't support Device Owner mode
)

echo.
echo 🔍 Verifying setup...
echo Device Owner status:
adb shell dpm list-owners

echo.
echo 🚀 Launching EMILocker app...
adb shell am start -n com.example.emilocker/.MainActivity

if %errorlevel% equ 0 (
    echo ✅ App launched successfully!
    echo.
    echo 🎉 SUCCESS! Your EMILocker app is now running on the emulator!
    echo.
    echo 📱 What you should see on the emulator:
    echo - EMILocker app should be open
    echo - Device Owner Status should show "GRANTED" or "NOT GRANTED"
    echo - EMI toggle switch (ON = PAID, OFF = NOT PAID)
    echo - Lock Device and Unlock Device buttons
    echo.
    echo 🧪 Test the app:
    echo 1. Toggle the EMI switch to OFF → Device should lock (Kiosk Mode)
    echo 2. Try pressing home/back buttons → Should be disabled when locked
    echo 3. Toggle EMI switch to ON → Device should unlock
    echo 4. Test manual Lock/Unlock buttons
    echo.
    echo 📊 Monitor app logs:
    echo adb logcat | findstr EMILocker
    echo.
) else (
    echo ❌ Failed to launch app. You can manually open it from the emulator.
)

echo 🎯 App is ready for testing!
echo.
pause

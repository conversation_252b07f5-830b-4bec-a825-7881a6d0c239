@echo off
echo.
echo ========================================
echo    EMILocker Testing Setup Script
echo ========================================
echo.

REM Check if ADB is available
adb version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ADB not found! Please install Android SDK Platform Tools
    echo Download from: https://developer.android.com/studio/releases/platform-tools
    pause
    exit /b 1
)

echo ✅ ADB found!
echo.

REM Check for connected devices
echo Checking for connected devices...
adb devices
echo.

REM Ask user for testing method
echo Choose your testing method:
echo 1. Android Emulator (Recommended)
echo 2. Physical Device (Limited testing)
echo 3. Physical Device (Full setup - requires removing accounts)
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto emulator_setup
if "%choice%"=="2" goto physical_limited
if "%choice%"=="3" goto physical_full
echo Invalid choice!
pause
exit /b 1

:emulator_setup
echo.
echo 🚀 Setting up for Android Emulator...
echo.
echo Please make sure you have:
echo - Android Studio installed
echo - An AVD created (API 28+ without Google Play)
echo - Emulator running with fresh/wiped data
echo.
echo Press any key when emulator is ready...
pause
goto build_and_install

:physical_limited
echo.
echo 📱 Setting up for Physical Device (Limited Testing)...
echo.
echo ⚠️  Note: Without Device Owner privileges, you can only test:
echo    - UI functionality
echo    - Toggle switches
echo    - Error messages
echo    - App flow
echo.
echo ❌ You CANNOT test actual device locking functionality
echo.
echo Press any key to continue...
pause
goto build_and_install

:physical_full
echo.
echo 📱 Setting up for Physical Device (Full Testing)...
echo.
echo ⚠️  WARNING: This will temporarily make your device managed!
echo.
echo Before proceeding:
echo 1. Backup your important data
echo 2. Remove Google account from Settings → Accounts
echo 3. Restart your device
echo.
echo Have you completed the above steps? (y/n)
set /p confirm="Enter y to continue: "
if /i not "%confirm%"=="y" (
    echo Setup cancelled.
    pause
    exit /b 1
)
goto build_and_install

:build_and_install
echo.
echo 🔨 Building the app...
call gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo ❌ Build failed!
    pause
    exit /b 1
)

echo ✅ Build successful!
echo.

echo 📦 Installing the app...
adb install -r app\build\outputs\apk\debug\app-debug.apk
if %errorlevel% neq 0 (
    echo ❌ Installation failed!
    pause
    exit /b 1
)

echo ✅ App installed successfully!
echo.

REM Try to set device owner based on choice
if "%choice%"=="1" goto set_device_owner
if "%choice%"=="2" goto skip_device_owner
if "%choice%"=="3" goto set_device_owner

:set_device_owner
echo 🔐 Setting up Device Owner privileges...
adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver
if %errorlevel% neq 0 (
    echo ❌ Failed to set Device Owner!
    echo.
    echo Possible reasons:
    echo - Device has existing accounts
    echo - Another app is already Device Owner
    echo - Device doesn't support Device Owner
    echo.
    echo You can still test the app with limited functionality.
    goto test_instructions
)

echo ✅ Device Owner set successfully!
echo.

REM Verify device owner status
echo 🔍 Verifying Device Owner status...
adb shell dpm list-owners
echo.

goto test_instructions

:skip_device_owner
echo ⏭️  Skipping Device Owner setup (limited testing mode)
echo.

:test_instructions
echo.
echo ========================================
echo           🎉 Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Launch EMILocker app on your device/emulator
echo 2. Grant Device Admin permissions when prompted
echo 3. Check if app shows "Device Owner Status: GRANTED"
echo 4. Test the EMI toggle switch
echo 5. Try the Lock/Unlock buttons
echo.

if "%choice%"=="1" (
    echo 📱 Emulator Testing:
    echo - Full functionality should work
    echo - Test all features from TESTING.md
)

if "%choice%"=="2" (
    echo 📱 Physical Device (Limited):
    echo - UI and basic functionality will work
    echo - Device locking won't work without Device Owner
    echo - Check error messages and app behavior
)

if "%choice%"=="3" (
    echo 📱 Physical Device (Full):
    echo - All functionality should work
    echo - Test carefully - device will actually lock!
    echo - Use EMI switch to unlock if needed
)

echo.
echo 📋 For detailed testing procedures, see:
echo    - TESTING.md (comprehensive test cases)
echo    - EMULATOR_TESTING.md (emulator-specific guide)
echo    - PHYSICAL_DEVICE_TESTING.md (physical device guide)
echo.

echo 🔧 Useful commands:
echo    adb shell dpm list-owners          (check device owner)
echo    adb logcat ^| findstr EMILocker     (view app logs)
echo    adb shell am force-stop com.example.emilocker  (stop app)
echo.

echo Press any key to exit...
pause

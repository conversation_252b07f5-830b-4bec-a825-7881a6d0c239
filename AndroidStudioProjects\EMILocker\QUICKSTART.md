# 🚀 EMILocker Quick Start Guide

Get your EMILocker app running in 5 minutes!

## ⚡ Super Quick Setup (Windows)

### Option 1: Automated Setup Script
1. **Connect** your Android device via USB
2. **Enable** USB Debugging in Developer Options
3. **Factory Reset** your device (IMPORTANT!)
4. **Skip** initial setup wizard (don't add Google account)
5. **Run** the setup script:
   ```cmd
   cd AndroidStudioProjects\EMILocker
   setup.bat
   ```

### Option 2: Manual Setup
```bash
# 1. Build the app
gradlew.bat assembleDebug

# 2. Install the app (don't launch yet!)
adb install app\build\outputs\apk\debug\app-debug.apk

# 3. Set as Device Owner (CRITICAL STEP)
adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver

# 4. Launch the app and test!
```

## 🎯 How to Use

### Main Features
- **EMI Toggle Switch**: 
  - `ON` = EMI Paid → Device Unlocked 🔓
  - `OFF` = EMI Not Paid → Device Locked 🔒

- **Manual Controls**:
  - `LOCK DEVICE` button → Immediate lock
  - `UNLOCK DEVICE` button → Immediate unlock

### Testing the Lock
1. **Turn OFF** the EMI switch
2. **Device locks immediately** - you can't:
   - Press home button
   - Press back button  
   - Access notifications
   - Switch apps
   - Exit the app

3. **Turn ON** the EMI switch
4. **Device unlocks** - normal functionality restored

## 🔧 Troubleshooting

### "App is not Device Owner" Error
**Solution**: Device needs factory reset before setup
```bash
# Check current status
adb shell dpm list-owners

# If another owner exists, remove it
adb shell dpm remove-active-admin [package-name]

# Factory reset and try again
```

### ADB Not Found
**Solution**: Install Android SDK Platform Tools
1. Download from: https://developer.android.com/studio/releases/platform-tools
2. Extract and add to PATH
3. Test: `adb devices`

### Device Not Detected
**Solution**: Enable USB Debugging
1. Go to Settings → About Phone
2. Tap Build Number 7 times
3. Go to Developer Options
4. Enable USB Debugging
5. Connect via USB and allow debugging

## 📱 Supported Devices

- **Android 7.0+** (API 24+)
- **Physical devices only** (emulators won't work)
- **Fresh devices** (factory reset required)

## ⚠️ Important Warnings

- **Use only on test devices** - Device Owner is powerful
- **Backup data** before factory reset
- **Cannot uninstall easily** when set as Device Owner
- **Factory reset removes** Device Owner privileges

## 🎉 Success Indicators

You'll know it's working when:
- ✅ App shows "Device Owner Status: GRANTED"
- ✅ Toggle switch locks/unlocks device immediately
- ✅ Home/back buttons don't work when locked
- ✅ Toast messages appear for lock/unlock actions

## 📞 Need Help?

1. **Check README.md** - Comprehensive documentation
2. **Check TESTING.md** - Detailed testing procedures
3. **Run diagnostics**:
   ```bash
   adb shell dpm list-owners
   adb logcat | grep EMILocker
   ```

---

**🎯 Goal**: Complete phone lockdown when EMI not paid, full access when EMI paid!

**⏱️ Setup Time**: ~5 minutes with fresh device

**🔒 Security Level**: Maximum (Device Owner privileges)**

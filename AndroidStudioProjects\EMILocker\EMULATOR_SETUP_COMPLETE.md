# 🎯 Complete Emulator Setup Guide for EMILocker

## ✅ What We've Accomplished So Far

1. **✅ Emulator is Running**: Your Android emulator is up and running
2. **✅ ADB is Working**: Device detected as `emulator-5554`
3. **✅ Gradle Wrapper Fixed**: Downloaded missing gradle-wrapper.jar
4. **✅ Icons Created**: Added missing app icons (ic_launcher.png)
5. **✅ Java Detected**: Found Java in Android Studio installation

## 🚀 Next Steps to Complete Setup

### **Option 1: Use Android Studio (Recommended)**

This is the easiest and most reliable method:

1. **Open Android Studio**
2. **File → Open** → Navigate to: `C:\Users\<USER>\AndroidStudioProjects\EMILocker`
3. **Wait for project sync** (Android Studio will download dependencies automatically)
4. **Build the app**: 
   - Click **Build → Build Bundle(s) / APK(s) → Build APK(s)**
   - Or use **Build → Make Project** (Ctrl+F9)
5. **APK will be created** at: `app\build\outputs\apk\debug\app-debug.apk`

### **Option 2: Command Line Build (If Android Studio isn't available)**

If you prefer command line or Android Studio isn't working:

```bash
# Navigate to project directory
cd AndroidStudioProjects\EMILocker

# Try building with different Java paths
set JAVA_HOME=C:\Program Files\Android\Android Studio\jbr
gradlew.bat assembleDebug

# Or try with system Java (if installed)
set JAVA_HOME=C:\Program Files\Java\jdk-11.0.x
gradlew.bat assembleDebug
```

## 📱 Installing and Testing the App

Once you have the APK built:

### **Step 1: Install the App**
```bash
cd AndroidStudioProjects\EMILocker
adb install -r app\build\outputs\apk\debug\app-debug.apk
```

### **Step 2: Set Device Owner (Critical!)**
```bash
adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver
```

**Expected output:**
```
Success: Device owner set to ComponentInfo{com.example.emilocker/com.example.emilocker.MyDeviceAdminReceiver}
```

### **Step 3: Verify Setup**
```bash
# Check device owner status
adb shell dpm list-owners

# Should show:
# Device owner (User 0): ComponentInfo{com.example.emilocker/com.example.emilocker.MyDeviceAdminReceiver}
```

### **Step 4: Launch and Test**
1. **Launch EMILocker** app on the emulator
2. **Grant Device Admin** permissions when prompted
3. **Check status**: App should show "Device Owner Status: GRANTED"
4. **Test EMI toggle**: Switch should lock/unlock the device
5. **Test manual buttons**: Lock/Unlock buttons should work

## 🧪 Testing Checklist

- [ ] App launches without crashing
- [ ] Shows "Device Owner Status: GRANTED"
- [ ] EMI toggle switch changes between PAID/NOT PAID
- [ ] Device locks when EMI switch is OFF (Kiosk Mode)
- [ ] Device unlocks when EMI switch is ON
- [ ] Manual Lock/Unlock buttons work
- [ ] Toast messages appear correctly
- [ ] Home button disabled when locked
- [ ] Back button shows warning when locked

## 🔧 Troubleshooting

### **Build Issues**
- **Java not found**: Install Java JDK 8 or 11, set JAVA_HOME
- **Gradle issues**: Use Android Studio to sync project first
- **Missing dependencies**: Let Android Studio download them automatically

### **Device Owner Issues**
- **"Not allowed to set device owner"**: Emulator might have accounts, try wiping emulator data
- **Command fails**: Make sure emulator is fresh (started with `-wipe-data`)
- **Already has owner**: Run `adb shell dpm list-owners` to check existing owners

### **App Issues**
- **Crashes on launch**: Check `adb logcat | findstr EMILocker` for errors
- **Permissions denied**: Make sure Device Owner was set correctly
- **Lock doesn't work**: Verify Device Owner status in app

## 🎯 Quick Commands Reference

```bash
# Build (if Gradle is working)
gradlew.bat assembleDebug

# Install app
adb install -r app\build\outputs\apk\debug\app-debug.apk

# Set Device Owner
adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver

# Check Device Owner
adb shell dpm list-owners

# Launch app
adb shell am start -n com.example.emilocker/.MainActivity

# View logs
adb logcat | findstr EMILocker

# Force stop app
adb shell am force-stop com.example.emilocker

# Uninstall (if needed)
adb uninstall com.example.emilocker
```

## 🔄 Reset Emulator (If Needed)

If something goes wrong and you need to start fresh:

```bash
# Stop emulator
adb emu kill

# Start with clean state
emulator -avd Medium_Phone_API_36.0 -wipe-data
```

## 📊 Success Indicators

You'll know everything is working when:

1. **✅ Build completes** without errors
2. **✅ App installs** successfully on emulator
3. **✅ Device Owner** is set without errors
4. **✅ App shows** "Device Owner Status: GRANTED"
5. **✅ EMI toggle** actually locks/unlocks the device
6. **✅ Kiosk mode** prevents home/back button usage when locked

## 🎉 Final Notes

- **Emulator is perfect** for development and testing
- **No factory reset needed** (unlike physical devices)
- **Easy to reset** if something goes wrong
- **Full functionality** available with Device Owner privileges
- **Safe environment** for testing device management features

---

**🎯 Current Status**: Emulator ready, icons fixed, ready for build and install!

**⏭️ Next Step**: Build the APK using Android Studio or command line, then install and test!

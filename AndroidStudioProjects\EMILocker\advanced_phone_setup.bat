@echo off
echo.
echo ========================================
echo    EMILocker Advanced Phone Setup
echo ========================================
echo.

echo 📱 This script provides multiple setup options for real phones
echo.

REM Check if phone is connected
echo 🔍 Checking for connected phone...
adb devices | findstr "device" | findstr -v "List" >nul
if %errorlevel% neq 0 (
    echo ❌ No phone detected!
    echo.
    echo 📋 Please:
    echo 1. Connect your phone via USB
    echo 2. Enable USB Debugging in Developer Options
    echo 3. Accept the USB debugging prompt on your phone
    echo 4. Run this script again
    echo.
    pause
    exit /b 1
)

echo ✅ Phone detected!
echo.

REM Show connected devices
echo 📱 Connected devices:
adb devices
echo.

REM Check current accounts
echo 🔍 Checking current accounts on device...
adb shell dumpsys account | findstr "Account {" >nul
if %errorlevel% equ 0 (
    echo ⚠️ Accounts detected on device
    set ACCOUNTS_PRESENT=1
) else (
    echo ✅ No accounts detected
    set ACCOUNTS_PRESENT=0
)

echo.
echo 🎯 Choose setup method:
echo.
echo 1. 📱 Install Alternative Mode Only (Works immediately - Recommended)
echo 2. 🔐 Full Device Owner Setup (Requires account removal)
echo 3. 👔 Work Profile Setup (Partial security, no account removal)
echo 4. 🔍 Check Device Owner Status
echo 5. 🗑️ Remove Device Owner
echo 6. 📊 Show Account Information
echo.

set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto alternative_mode
if "%choice%"=="2" goto device_owner_setup
if "%choice%"=="3" goto work_profile_setup
if "%choice%"=="4" goto check_status
if "%choice%"=="5" goto remove_owner
if "%choice%"=="6" goto show_accounts
goto invalid_choice

:alternative_mode
echo.
echo 📱 ALTERNATIVE MODE SETUP (Recommended)
echo.
echo ✅ This mode works immediately on any phone!
echo.
echo 📦 Installing EMILocker APK...
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    adb install -r "app\build\outputs\apk\debug\app-debug.apk"
    if %errorlevel% equ 0 (
        echo ✅ APK installed successfully!
        echo.
        echo 🎯 Alternative Mode Features:
        echo • ✅ App stays in foreground during lock
        echo • ✅ Back button blocked (3-press override for safety)
        echo • ✅ Fullscreen mode with hidden navigation
        echo • ✅ Screen stays on during lock
        echo • ⚠️ Home button shows warnings but allows exit
        echo • ✅ Professional UI with status monitoring
        echo.
        echo 🚀 Ready to test! Open EMILocker app on your phone.
    ) else (
        echo ❌ APK installation failed!
    )
) else (
    echo ❌ APK file not found! Please build the app first.
)
goto end

:device_owner_setup
echo.
echo 🔐 FULL DEVICE OWNER SETUP
echo.
if %ACCOUNTS_PRESENT%==1 (
    echo ⚠️ WARNING: Accounts detected on your device!
    echo.
    echo 📋 Device Owner requires NO accounts on the device.
    echo.
    echo 🔧 To proceed, you must:
    echo 1. 💾 Backup your important data (photos, contacts)
    echo 2. 🗑️ Remove ALL accounts from Settings → Accounts:
    echo    • Google Account
    echo    • Samsung Account  
    echo    • Microsoft Account
    echo    • Any other accounts
    echo 3. 🔄 Restart your phone
    echo 4. 🔌 Reconnect and run this script again
    echo.
    echo 💡 Alternative: Choose option 1 (Alternative Mode) or 3 (Work Profile)
    echo.
    set /p proceed="Do you want to continue anyway? (y/n): "
    if /i not "%proceed%"=="y" goto end
)

echo.
echo 📦 Installing APK...
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    adb install -r "app\build\outputs\apk\debug\app-debug.apk"
    if %errorlevel% neq 0 (
        echo ❌ APK installation failed!
        goto end
    )
) else (
    echo ❌ APK file not found!
    goto end
)

echo.
echo 🔐 Attempting to set Device Owner...
adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver

if %errorlevel% equ 0 (
    echo.
    echo 🎉 SUCCESS! Device Owner set successfully!
    echo.
    echo ✅ Maximum Security Active:
    echo • 🔒 Complete device lockdown capability
    echo • 🚫 Home/back buttons completely disabled when locked
    echo • 🚫 Notifications blocked during lock
    echo • 🚫 App switching prevented
    echo • 🚫 All bypass methods blocked
    echo.
    echo 📱 You can now safely re-add your accounts.
) else (
    echo.
    echo ❌ Device Owner setup FAILED!
    echo.
    echo 🔧 Common reasons:
    echo • 📱 Google account still present
    echo • 👤 Other accounts logged in
    echo • 🔄 Phone needs restart after account removal
    echo • 🏢 Another app is already Device Owner
    echo.
    echo 💡 Solutions:
    echo 1. 🗑️ Double-check ALL accounts are removed
    echo 2. 🔄 Restart your phone completely
    echo 3. 📱 Try Alternative Mode (option 1) instead
    echo 4. 👔 Try Work Profile setup (option 3)
)
goto end

:work_profile_setup
echo.
echo 👔 WORK PROFILE SETUP
echo.
echo ✅ This method keeps your accounts and provides partial security.
echo.

echo 📦 Installing APK in main profile...
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    adb install -r "app\build\outputs\apk\debug\app-debug.apk"
) else (
    echo ❌ APK file not found!
    goto end
)

echo.
echo 👔 Creating work profile...
adb shell pm create-user --profileOf 0 --managed work_profile

echo.
echo 📦 Installing APK in work profile...
adb install --user 10 "app\build\outputs\apk\debug\app-debug.apk"

echo.
echo 🔐 Setting profile owner...
adb shell dpm set-profile-owner --user 10 com.example.emilocker/.MyDeviceAdminReceiver

if %errorlevel% equ 0 (
    echo.
    echo ✅ Work Profile setup successful!
    echo.
    echo 📱 Features available:
    echo • ✅ Partial device management
    echo • ✅ App isolation in work profile
    echo • ✅ Some kiosk capabilities
    echo • ✅ Your personal accounts remain untouched
    echo.
    echo 🎯 Look for EMILocker in your work profile apps.
) else (
    echo ❌ Work Profile setup failed!
    echo 💡 Try Alternative Mode (option 1) instead.
)
goto end

:check_status
echo.
echo 🔍 DEVICE OWNER STATUS CHECK
echo.
echo Device Owners:
adb shell dpm list-owners
echo.
echo Device Policy Info:
adb shell dumpsys device_policy | findstr -i "device owner\|profile owner"
echo.
goto end

:remove_owner
echo.
echo 🗑️ REMOVING DEVICE OWNER
echo.
adb shell dpm remove-active-admin com.example.emilocker/.MyDeviceAdminReceiver
echo.
echo ✅ Device Owner removed (if it was set)
echo.
goto end

:show_accounts
echo.
echo 📊 ACCOUNT INFORMATION
echo.
echo Accounts on device:
adb shell dumpsys account | findstr "Account {"
echo.
echo User info:
adb shell pm list users
echo.
goto end

:invalid_choice
echo.
echo ❌ Invalid choice! Please run the script again.
echo.

:end
echo.
echo 🎯 Setup Summary:
echo.
echo 📱 Alternative Mode: Works on any phone immediately
echo 🔐 Device Owner: Maximum security, requires account removal  
echo 👔 Work Profile: Partial security, keeps your accounts
echo.
echo 🚀 Recommendation: Start with Alternative Mode for immediate testing!
echo.
pause

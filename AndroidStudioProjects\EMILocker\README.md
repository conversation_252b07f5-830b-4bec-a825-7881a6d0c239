# 🔒 EMILocker - Android Phone Locking System

A complete Kotlin Android app that simulates a phone-locking system based on EMI payment status using Android's Kiosk Mode (Lock Task Mode) and Device Owner privileges.

## 📱 Features

- **Complete Device Locking**: Uses `DevicePolicyManager` with `startLockTask()` and `stopLockTask()`
- **EMI Payment Simulation**: Toggle switch to simulate EMI paid/not paid status
- **Kiosk Mode**: Completely restricts user access when `isBlocked = true`
- **Device Owner Integration**: Proper Device Admin Receiver implementation
- **Error Handling**: Detects and displays Device Owner privilege status
- **Modern UI**: Material Design with CardView layout
- **Manual Controls**: Lock/Unlock buttons for testing

## 🏗️ Project Structure

```
EMILocker/
├── app/
│   ├── src/main/
│   │   ├── java/com/example/emilocker/
│   │   │   ├── MainActivity.kt              # Main activity with lock/unlock logic
│   │   │   └── MyDeviceAdminReceiver.kt     # Device admin receiver
│   │   ├── res/
│   │   │   ├── layout/
│   │   │   │   └── activity_main.xml        # UI layout with toggle switch
│   │   │   ├── values/
│   │   │   │   ├── colors.xml               # App colors
│   │   │   │   ├── strings.xml              # App strings
│   │   │   │   └── themes.xml               # App themes
│   │   │   └── xml/
│   │   │       ├── device_admin_receiver.xml # Device admin policies
│   │   │       ├── backup_rules.xml
│   │   │       └── data_extraction_rules.xml
│   │   └── AndroidManifest.xml              # Permissions and declarations
│   ├── build.gradle.kts                     # App-level dependencies
│   └── proguard-rules.pro
├── build.gradle.kts                         # Project-level configuration
├── settings.gradle.kts
├── gradle.properties
└── README.md
```

## 🔧 Setup Instructions

### Prerequisites

1. **Android Studio** (Latest version recommended)
2. **Android SDK** with API level 24+ (Android 7.0+)
3. **Physical Android Device** (Emulator won't work for Device Owner setup)
4. **USB Debugging** enabled on the device
5. **ADB (Android Debug Bridge)** installed and accessible

### Step 1: Clone and Build

1. Open Android Studio
2. Import the EMILocker project
3. Sync Gradle files
4. Build the project (`Build > Make Project`)

### Step 2: Install the App

1. Connect your Android device via USB
2. Enable Developer Options and USB Debugging
3. Install the app:
   ```bash
   ./gradlew installDebug
   ```
   Or use Android Studio's Run button

### Step 3: Set Device Owner (CRITICAL)

⚠️ **IMPORTANT**: This step must be done on a **fresh device** or after a **factory reset**, before launching the app for the first time.

1. **Factory Reset** your test device (if not fresh)
2. **Skip** the initial setup wizard (don't add Google account)
3. **Enable Developer Options** and **USB Debugging**
4. **Connect** device to computer via USB
5. **Install** the EMILocker app (but don't launch it yet)
6. **Run the ADB command**:

```bash
adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver
```

Expected output:
```
Success: Device owner set to package com.example.emilocker
Active admin set to component {com.example.emilocker/com.example.emilocker.MyDeviceAdminReceiver}
```

### Step 4: Launch and Test

1. **Launch** the EMILocker app
2. **Grant** Device Admin permissions when prompted
3. **Test** the toggle switch:
   - **OFF** (EMI Not Paid) = Device locks in Kiosk Mode
   - **ON** (EMI Paid) = Device unlocks from Kiosk Mode

## 🎯 How It Works

### Core Logic

```kotlin
// Main variable controlling EMI status
private var isBlocked: Boolean = false

// When EMI is not paid (isBlocked = true)
private fun lockDevice() {
    startLockTask() // Enters Kiosk Mode
}

// When EMI is paid (isBlocked = false)  
private fun unlockDevice() {
    stopLockTask() // Exits Kiosk Mode
}
```

### Device Owner Privileges

The app requires Device Owner privileges to:
- Use `startLockTask()` and `stopLockTask()`
- Prevent users from exiting the app
- Block home button, back button, and notifications
- Control device access completely

## 🔐 Security Features

- **Kiosk Mode**: Complete device lockdown when EMI not paid
- **Back Button Blocking**: Prevents escape when device is locked
- **Home Button Blocking**: User cannot access home screen
- **Notification Blocking**: No access to notification panel
- **App Switching Prevention**: Cannot switch to other apps

## 🧪 Testing

### Test Scenarios

1. **Toggle Switch Test**:
   - Switch OFF → Device should lock immediately
   - Switch ON → Device should unlock immediately

2. **Manual Button Test**:
   - Press "LOCK DEVICE" → Should enter Kiosk Mode
   - Press "UNLOCK DEVICE" → Should exit Kiosk Mode

3. **Back Button Test**:
   - When locked, back button should show toast message
   - When unlocked, back button should work normally

4. **Device Owner Test**:
   - App should display Device Owner status
   - Should show warning if not Device Owner

## 🚨 Troubleshooting

### Common Issues

1. **"App is not Device Owner" Error**:
   - Solution: Run the ADB command on a fresh device
   - Make sure device is factory reset before setup

2. **ADB Command Fails**:
   - Check USB debugging is enabled
   - Try `adb devices` to verify connection
   - Make sure app is installed but not launched

3. **Lock Task Mode Not Working**:
   - Verify Device Owner status in app
   - Check AndroidManifest.xml permissions
   - Ensure `lockTaskMode="if_whitelisted"` is set

4. **Cannot Exit Kiosk Mode**:
   - Use the toggle switch or unlock button
   - If stuck, restart the device

### ADB Commands Reference

```bash
# Check if device is connected
adb devices

# Set device owner (MUST be done on fresh device)
adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver

# Check device owner status
adb shell dpm list-owners

# Remove device owner (if needed)
adb shell dpm remove-active-admin com.example.emilocker/.MyDeviceAdminReceiver

# Install app
adb install app-debug.apk

# Uninstall app
adb uninstall com.example.emilocker
```

## 📋 Requirements Met

✅ **DevicePolicyManager Integration**: Uses `startLockTask()` and `stopLockTask()`  
✅ **Boolean Variable**: `isBlocked` controls EMI payment status  
✅ **Toggle Switch UI**: Manual EMI payment status control  
✅ **Device Admin Receiver**: `MyDeviceAdminReceiver.kt` with XML policies  
✅ **Error Handling**: Device Owner privilege detection and messages  
✅ **Complete Kiosk Mode**: Full device access restriction  
✅ **Modern Kotlin**: Written in Kotlin with modern Android practices  
✅ **Target SDK 33**: Android 13+ compatibility  

## 🔄 Future Enhancements

- **Backend Integration**: Connect to real EMI payment API
- **Automatic Reboot Handling**: Re-enter Kiosk Mode after device restart
- **Scheduled Checks**: Periodic EMI status verification
- **Admin Panel**: Remote device management
- **Multiple Device Support**: Fleet management capabilities

## 📄 License

This project is for educational and demonstration purposes. Use responsibly and in compliance with local laws and regulations.

## ⚠️ Important Notes

- **Device Owner privileges are powerful** - use only on test devices
- ****Factory reset removes Device Owner** - setup required after reset  
- **Cannot be uninstalled easily** when set as Device Owner
- **Test thoroughly**** before deploying to production devices
- **Backup important data** before testing on any device

---

**Built with ❤️ using Kotlin and Android's Device Policy Manager**

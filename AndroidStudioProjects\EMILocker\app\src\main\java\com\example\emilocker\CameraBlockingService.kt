package com.example.emilocker

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.NotificationCompat
import android.widget.Toast
import java.util.*
import kotlin.concurrent.timer

class CameraBlockingService : Service() {
    
    private var monitoringTimer: Timer? = null
    private val NOTIFICATION_ID = 1001
    private val CHANNEL_ID = "camera_blocking_channel"
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification())
        startCameraMonitoring()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_STICKY // Restart if killed
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        return null
    }
    
    override fun onDestroy() {
        super.onDestroy()
        monitoringTimer?.cancel()
        monitoringTimer = null
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Camera Blocking Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Monitors and blocks camera access"
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification() = NotificationCompat.Builder(this, CHANNEL_ID)
        .setContentTitle("📷 Camera Blocked")
        .setContentText("EMILocker is blocking camera access")
        .setSmallIcon(android.R.drawable.ic_menu_camera)
        .setOngoing(true)
        .setContentIntent(
            PendingIntent.getActivity(
                this, 0,
                Intent(this, MainActivity::class.java),
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
        )
        .build()
    
    private fun startCameraMonitoring() {
        monitoringTimer = timer(period = 2000) { // Check every 2 seconds
            checkForCameraApps()
        }
    }
    
    private fun checkForCameraApps() {
        try {
            val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            val runningApps = activityManager.getRunningTasks(10)
            
            for (task in runningApps) {
                val topActivity = task.topActivity?.className
                
                // Check for common camera apps
                if (isCameraApp(topActivity)) {
                    blockCameraApp(task.topActivity?.packageName)
                }
            }
        } catch (e: Exception) {
            // Handle permission issues gracefully
        }
    }
    
    private fun isCameraApp(className: String?): Boolean {
        if (className == null) return false
        
        val cameraKeywords = listOf(
            "camera", "Camera", "CAMERA",
            "photo", "Photo", "PHOTO",
            "snap", "Snap", "SNAP",
            "pic", "Pic", "PIC"
        )
        
        return cameraKeywords.any { className.contains(it, ignoreCase = true) }
    }
    
    private fun blockCameraApp(packageName: String?) {
        if (packageName == null || packageName == this.packageName) return
        
        try {
            // Show blocking notification
            showCameraBlockedToast()
            
            // Try to bring EMILocker to front
            val intent = Intent(this, MainActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
            }
            startActivity(intent)
            
        } catch (e: Exception) {
            // Handle gracefully
        }
    }
    
    private fun showCameraBlockedToast() {
        // Create a handler to show toast on main thread
        android.os.Handler(android.os.Looper.getMainLooper()).post {
            Toast.makeText(
                this,
                "📷 Camera access blocked by EMILocker! Pay EMI to unlock.",
                Toast.LENGTH_LONG
            ).show()
        }
    }
}

@echo off
echo.
echo ========================================
echo    Creating Missing App Icons
echo ========================================
echo.

REM Create directories for different icon densities
mkdir "app\src\main\res\mipmap-mdpi" 2>nul
mkdir "app\src\main\res\mipmap-hdpi" 2>nul
mkdir "app\src\main\res\mipmap-xhdpi" 2>nul
mkdir "app\src\main\res\mipmap-xxhdpi" 2>nul
mkdir "app\src\main\res\mipmap-xxxhdpi" 2>nul

echo Creating placeholder icons...

REM Create a simple placeholder icon using PowerShell
powershell -Command "& {
    Add-Type -AssemblyName System.Drawing
    
    # Create different sizes for different densities
    $sizes = @{
        'mdpi' = 48
        'hdpi' = 72
        'xhdpi' = 96
        'xxhdpi' = 144
        'xxxhdpi' = 192
    }
    
    foreach ($density in $sizes.Keys) {
        $size = $sizes[$density]
        
        # Create bitmap
        $bitmap = New-Object System.Drawing.Bitmap($size, $size)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # Fill with blue background
        $brush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(33, 150, 243))
        $graphics.FillRectangle($brush, 0, 0, $size, $size)
        
        # Add white text 'E'
        $font = New-Object System.Drawing.Font('Arial', ($size * 0.6), [System.Drawing.FontStyle]::Bold)
        $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
        $textSize = $graphics.MeasureString('E', $font)
        $x = ($size - $textSize.Width) / 2
        $y = ($size - $textSize.Height) / 2
        $graphics.DrawString('E', $font, $textBrush, $x, $y)
        
        # Save as PNG
        $path = \"app\src\main\res\mipmap-$density\ic_launcher.png\"
        $bitmap.Save($path, [System.Drawing.Imaging.ImageFormat]::Png)
        
        # Create round version (same for now)
        $pathRound = \"app\src\main\res\mipmap-$density\ic_launcher_round.png\"
        $bitmap.Save($pathRound, [System.Drawing.Imaging.ImageFormat]::Png)
        
        Write-Host \"Created icons for $density density\"
        
        # Cleanup
        $graphics.Dispose()
        $bitmap.Dispose()
        $brush.Dispose()
        $textBrush.Dispose()
        $font.Dispose()
    }
    
    Write-Host 'All icons created successfully!'
}"

if %errorlevel% neq 0 (
    echo.
    echo ❌ PowerShell icon creation failed. Creating simple placeholder files...
    echo.
    
    REM Create empty placeholder files if PowerShell fails
    echo. > "app\src\main\res\mipmap-hdpi\ic_launcher.png"
    echo. > "app\src\main\res\mipmap-hdpi\ic_launcher_round.png"
    echo. > "app\src\main\res\mipmap-mdpi\ic_launcher.png"
    echo. > "app\src\main\res\mipmap-mdpi\ic_launcher_round.png"
    echo. > "app\src\main\res\mipmap-xhdpi\ic_launcher.png"
    echo. > "app\src\main\res\mipmap-xhdpi\ic_launcher_round.png"
    echo. > "app\src\main\res\mipmap-xxhdpi\ic_launcher.png"
    echo. > "app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png"
    echo. > "app\src\main\res\mipmap-xxxhdpi\ic_launcher.png"
    echo. > "app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png"
    
    echo ⚠️  Created placeholder files. You may need to replace with proper icons later.
) else (
    echo ✅ Icons created successfully!
)

echo.
echo Verifying icon files...
dir "app\src\main\res\mipmap-hdpi\ic_launcher*"

echo.
echo ✅ Icon creation complete!
echo.
pause

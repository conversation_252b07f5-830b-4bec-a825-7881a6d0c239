@echo off
echo.
echo ========================================
echo    Simple EMILocker Build & Install
echo ========================================
echo.

echo 🎯 Since Gradle build is having Java issues, let's try a different approach:
echo.
echo Option 1: Use Android Studio (Recommended)
echo 1. Open Android Studio
echo 2. Open this project folder: %CD%
echo 3. Let Android Studio sync the project
echo 4. Click Build → Build Bundle(s) / APK(s) → Build APK(s)
echo 5. Once built, the APK will be in app\build\outputs\apk\debug\
echo.

echo Option 2: Manual APK Installation (if you have a pre-built APK)
echo.

REM Check if there's already a built APK
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✅ Found existing APK: app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo Installing to emulator...
    adb install -r "app\build\outputs\apk\debug\app-debug.apk"
    
    if %errorlevel% equ 0 (
        echo ✅ APK installed successfully!
        echo.
        echo 🔐 Now setting up Device Owner privileges...
        adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver
        
        if %errorlevel% equ 0 (
            echo ✅ Device Owner set successfully!
            echo.
            echo 🎉 Setup Complete! You can now:
            echo 1. Launch EMILocker app on the emulator
            echo 2. Grant Device Admin permissions when prompted
            echo 3. Test the EMI toggle switch
            echo 4. Verify lock/unlock functionality
        ) else (
            echo ❌ Failed to set Device Owner. This might be because:
            echo - Device has existing accounts
            echo - Another app is already Device Owner
            echo - Emulator doesn't support Device Owner
            echo.
            echo You can still test the app with limited functionality.
        )
    ) else (
        echo ❌ APK installation failed!
    )
    
    goto end
)

echo ❌ No pre-built APK found.
echo.
echo 📋 To build the app, you have these options:
echo.
echo 1. **Android Studio (Easiest)**:
echo    - Open Android Studio
echo    - File → Open → Select this folder
echo    - Wait for sync to complete
echo    - Build → Build Bundle(s) / APK(s) → Build APK(s)
echo.
echo 2. **Fix Gradle Build**:
echo    - Install Java JDK 8 or 11
echo    - Set JAVA_HOME environment variable
echo    - Run: gradlew.bat assembleDebug
echo.
echo 3. **Use Pre-built APK**:
echo    - If you have a pre-built APK, copy it to:
echo      app\build\outputs\apk\debug\app-debug.apk
echo    - Then run this script again
echo.

:end
echo.
echo 📱 Current emulator status:
adb devices

echo.
echo 🔍 Useful commands for testing:
echo    adb shell dpm list-owners          (check device owner)
echo    adb logcat ^| findstr EMILocker     (view app logs)
echo    adb shell am start -n com.example.emilocker/.MainActivity  (launch app)
echo.

echo Press any key to exit...
pause >nul

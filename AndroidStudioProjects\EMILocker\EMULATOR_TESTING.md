# 📱 EMILocker Emulator Testing Guide

## 🚀 Quick Setup for Android Emulator

### Step 1: Create a New AVD (Android Virtual Device)
1. Open Android Studio
2. Go to **Tools → AVD Manager**
3. Click **Create Virtual Device**
4. Choose a device (Pixel 4 recommended)
5. Select **API 28 or higher** (Android 9.0+)
6. **Important**: Choose **Google APIs** (not Google Play)
7. Click **Finish**

### Step 2: Start Fresh Emulator
```bash
# Start emulator with clean state
emulator -avd [Your_AVD_Name] -wipe-data
```

### Step 3: Build and Install App
```bash
cd AndroidStudioProjects\EMILocker

# Build the app
gradlew.bat assembleDebug

# Install app (don't launch yet!)
adb install app\build\outputs\apk\debug\app-debug.apk
```

### Step 4: Set Device Owner (Critical!)
```bash
# Set as Device Owner - this is the key step
adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver
```

### Step 5: Launch and Test
1. Launch EMILocker app from emulator
2. Grant Device Admin permissions when prompted
3. Test the EMI toggle switch
4. Verify lock/unlock functionality

## 🔧 Emulator-Specific Commands

### Check Device Owner Status
```bash
adb shell dpm list-owners
# Should show: Device owner (User 0): ComponentInfo{com.example.emilocker/com.example.emilocker.MyDeviceAdminReceiver}
```

### Reset Emulator if Needed
```bash
# Stop emulator and wipe data
adb emu kill
emulator -avd [Your_AVD_Name] -wipe-data
```

### Debug Logging
```bash
# Monitor app logs
adb logcat | findstr EMILocker
```

## 🎯 Testing Checklist for Emulator

- [ ] App shows "Device Owner Status: GRANTED"
- [ ] EMI switch toggles between PAID/NOT PAID
- [ ] Device locks when EMI switch is OFF
- [ ] Device unlocks when EMI switch is ON
- [ ] Manual lock/unlock buttons work
- [ ] Toast messages appear correctly
- [ ] Home button disabled when locked
- [ ] Back button shows warning when locked

## ⚠️ Emulator Limitations

- Some hardware features may not work exactly like physical devices
- Performance might be slower
- Battery/power management differences
- Network connectivity differences

## 🔄 Quick Reset Process

If you need to reset the emulator:
1. Close emulator
2. Run: `emulator -avd [Your_AVD_Name] -wipe-data`
3. Repeat setup steps 3-5

## 📱 Alternative: Use Android Studio's Device Manager

1. **Tools → Device Manager**
2. **Create Device**
3. **Choose API 28+ without Google Play**
4. **Start with Cold Boot**
5. **Follow setup steps above**

---

**✅ Advantage**: No need for physical device or factory reset
**⚡ Speed**: Quick setup and testing
**🔄 Reset**: Easy to wipe and restart

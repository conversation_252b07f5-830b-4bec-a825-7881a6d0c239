@echo off
echo.
echo ========================================
echo    EMILocker Auto Setup for Fresh Devices
echo ========================================
echo.

echo 🎯 This script is designed for FRESH/FACTORY RESET devices
echo 📱 Perfect for new phones or devices without Google accounts
echo.

REM Check if phone is connected
echo 🔍 Checking for connected device...
adb devices | findstr "device" | findstr -v "List" >nul
if %errorlevel% neq 0 (
    echo ❌ No device detected!
    echo.
    echo 📋 Please:
    echo 1. Connect your FRESH device via USB
    echo 2. Enable USB Debugging in Developer Options
    echo 3. Accept the USB debugging prompt on your device
    echo 4. Run this script again
    echo.
    pause
    exit /b 1
)

echo ✅ Device detected!
echo.

REM Check if device is fresh (no accounts)
echo 🔍 Checking if device is fresh...
adb shell dumpsys account | findstr "Account {" >nul
if %errorlevel% equ 0 (
    echo ⚠️ WARNING: Accounts detected on device!
    echo.
    echo 📋 This device may not be fresh. For best results:
    echo 1. Factory reset the device
    echo 2. Skip Google account setup
    echo 3. Enable USB debugging immediately
    echo 4. Run this script before adding any accounts
    echo.
    set /p proceed="Continue anyway? (y/n): "
    if /i not "%proceed%"=="y" (
        echo Setup cancelled.
        pause
        exit /b 1
    )
) else (
    echo ✅ Fresh device confirmed - no accounts detected!
)

echo.
echo 🚀 Starting automatic setup...
echo.

REM Step 1: Build the latest APK
echo 📦 Step 1: Building latest EMILocker APK...
if exist "build_with_java.bat" (
    call build_with_java.bat
    if %errorlevel% neq 0 (
        echo ❌ Build failed!
        pause
        exit /b 1
    )
) else (
    echo ⚠️ Build script not found, using existing APK...
)

REM Step 2: Install APK
echo 📱 Step 2: Installing EMILocker on device...
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    adb install -r "app\build\outputs\apk\debug\app-debug.apk"
    if %errorlevel% neq 0 (
        echo ❌ APK installation failed!
        pause
        exit /b 1
    )
    echo ✅ APK installed successfully!
) else (
    echo ❌ APK file not found! Please build the project first.
    pause
    exit /b 1
)

REM Step 3: Set Device Owner (This is the key step for fresh devices)
echo 🔐 Step 3: Setting Device Owner (works on fresh devices)...
adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver

if %errorlevel% equ 0 (
    echo.
    echo 🎉 SUCCESS! Device Owner set successfully!
    echo.
    echo ✅ Your fresh device now has MAXIMUM SECURITY:
    echo • 📷 Hardware-level camera blocking
    echo • 🖼️ System-level wallpaper blocking
    echo • 🛡️ Factory reset protection
    echo • 🔒 Complete device lockdown capability
    echo • 🚫 Boot protection active
    echo.
) else (
    echo.
    echo ❌ Device Owner setup FAILED!
    echo.
    echo 🔧 This usually means:
    echo • Device is not actually fresh (has accounts)
    echo • Google Setup Wizard was completed
    echo • Another app is already Device Owner
    echo • USB debugging not properly enabled
    echo.
    echo 💡 Solutions:
    echo 1. Factory reset device completely
    echo 2. Skip ALL account setup during initial boot
    echo 3. Enable Developer Options immediately
    echo 4. Enable USB Debugging before adding accounts
    echo 5. Run this script BEFORE adding Google account
    echo.
    echo 📱 App will still work in Alternative Mode (90%% features)
)

REM Step 4: Launch the app
echo 🚀 Step 4: Launching EMILocker...
adb shell am start -n com.example.emilocker/.MainActivity

if %errorlevel% equ 0 (
    echo ✅ EMILocker launched successfully!
) else (
    echo ⚠️ App launch failed, please open manually
)

echo.
echo ========================================
echo    Setup Complete!
echo ========================================
echo.

REM Check final status
echo 📊 Final Status Check:
adb shell dpm list-owners

echo.
echo 🎯 What to do next:
echo 1. Check the app on your device
echo 2. Test the EMI toggle switch
echo 3. Test camera blocking toggle
echo 4. Test wallpaper blocking toggle
echo 5. Verify Device Owner status in app
echo.

if %errorlevel% equ 0 (
    echo 🎉 CONGRATULATIONS!
    echo Your fresh device is now protected with EMILocker's
    echo maximum security features!
) else (
    echo 📱 App installed in Alternative Mode
    echo Still provides 90%% of security features
    echo Perfect for testing and demonstrations
)

echo.
echo 💡 Pro Tip: This script works best on:
echo • Brand new phones
echo • Factory reset devices
echo • Devices that have never had Google accounts
echo • Custom ROMs or development builds
echo.

pause

@echo off
echo ========================================
echo    EMILocker Setup Script
echo ========================================
echo.

echo Step 1: Checking ADB connection...
adb devices
if %ERRORLEVEL% neq 0 (
    echo ERROR: ADB not found or device not connected!
    echo Please ensure:
    echo 1. Android device is connected via USB
    echo 2. USB Debugging is enabled
    echo 3. ADB is installed and in PATH
    pause
    exit /b 1
)

echo.
echo Step 2: Building the app...
call gradlew.bat assembleDebug
if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Installing the app...
adb install app\build\outputs\apk\debug\app-debug.apk
if %ERRORLEVEL% neq 0 (
    echo ERROR: Installation failed!
    pause
    exit /b 1
)

echo.
echo Step 4: Setting Device Owner...
echo IMPORTANT: Make sure this is a fresh device (factory reset)!
echo Press any key to continue or Ctrl+C to cancel...
pause > nul

adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to set Device Owner!
    echo This usually means:
    echo 1. Device is not fresh (needs factory reset)
    echo 2. App was already launched
    echo 3. Another device owner is already set
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Setup Complete!
echo ========================================
echo.
echo You can now launch the EMILocker app on your device.
echo The app should have Device Owner privileges and
echo can lock/unlock the device using Kiosk Mode.
echo.
echo Test the toggle switch:
echo - OFF = EMI Not Paid (Device Locked)
echo - ON = EMI Paid (Device Unlocked)
echo.
pause

# 📱 Physical Device Testing (No Factory Reset)

## 🎯 Option A: Secondary User Profile Method

### Step 1: Create Work Profile
```bash
# Check if device supports work profiles
adb shell pm list features | findstr android.software.managed_users

# Create a work profile (if supported)
adb shell pm create-user --profileOf 0 --managed work_profile
```

### Step 2: Install App in Work Profile
```bash
# Build app
gradlew.bat assembleDebug

# Install in work profile
adb install --user [work_profile_id] app\build\outputs\apk\debug\app-debug.apk

# Set as profile owner
adb shell dpm set-profile-owner --user [work_profile_id] com.example.emilocker/.MyDeviceAdminReceiver
```

## 🎯 Option B: Developer Testing Mode

### Step 1: Enable Developer Options
1. Go to **Settings → About Phone**
2. Tap **Build Number** 7 times
3. Go to **Settings → Developer Options**
4. Enable **USB Debugging**
5. Enable **Stay Awake**

### Step 2: Remove Existing Device Owners (If Any)
```bash
# Check current device owners
adb shell dpm list-owners

# Remove existing owners (if any)
adb shell dpm remove-active-admin [existing_package]/.AdminReceiver
```

### Step 3: Install and Test with Limited Permissions
```bash
# Build and install
gradlew.bat assembleDebug
adb install -r app\build\outputs\apk\debug\app-debug.apk

# Try to set device owner (might fail, but app will still work partially)
adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver
```

## 🎯 Option C: Temporary Device Owner (Risky)

⚠️ **Warning**: This temporarily makes your device a managed device

### Step 1: Backup Important Data
- Backup photos, contacts, apps
- Note: You can factory reset later to remove device owner

### Step 2: Remove Google Account Temporarily
1. Go to **Settings → Accounts**
2. Remove Google account
3. Restart device

### Step 3: Set Device Owner
```bash
# Now try setting device owner
adb shell dpm set-device-owner com.example.emilocker/.MyDeviceAdminReceiver
```

### Step 4: Test and Remove
```bash
# After testing, remove device owner
adb shell dpm remove-active-admin com.example.emilocker/.MyDeviceAdminReceiver

# Re-add your Google account
```

## 🔧 Testing Commands

### Check Device Status
```bash
# Device owner status
adb shell dpm list-owners

# Device admin status
adb shell dpm list-active-admins

# App permissions
adb shell dumpsys package com.example.emilocker | findstr permission
```

### Monitor App Behavior
```bash
# Real-time logging
adb logcat | findstr -i "emilocker\|devicepolicy\|admin"

# Check lock task mode
adb shell dumpsys activity | findstr -i "lock"
```

### Force Stop/Restart App
```bash
# Force stop
adb shell am force-stop com.example.emilocker

# Launch app
adb shell am start -n com.example.emilocker/.MainActivity
```

## 🎯 Limited Testing Without Device Owner

Even without Device Owner privileges, you can test:

### What Works:
- ✅ App UI and layout
- ✅ Toggle switch functionality
- ✅ Button interactions
- ✅ Toast messages
- ✅ Status text updates
- ✅ Basic app flow

### What Won't Work:
- ❌ Actual device locking (Kiosk Mode)
- ❌ Disabling home/back buttons
- ❌ Preventing app switching
- ❌ Full EMI locker functionality

### Modified Testing Approach:
1. **UI Testing**: Verify all UI elements work
2. **Logic Testing**: Check if app detects Device Owner status
3. **Error Handling**: Test error messages when not Device Owner
4. **State Management**: Verify app remembers toggle states

## 🚨 Troubleshooting

### "Not allowed to set device owner" Error
```bash
# Check if device has users
adb shell pm list users

# Check if accounts exist
adb shell dumpsys account

# Solution: Remove accounts or use emulator
```

### App Crashes
```bash
# Get crash logs
adb logcat | findstr -i "crash\|exception\|error"

# Clear app data
adb shell pm clear com.example.emilocker
```

### Permission Issues
```bash
# Grant all permissions manually
adb shell pm grant com.example.emilocker android.permission.BIND_DEVICE_ADMIN
```

## 📋 Testing Checklist (Limited Mode)

- [ ] App launches without crashing
- [ ] Shows "Device Owner Status: NOT GRANTED"
- [ ] EMI toggle switch changes text
- [ ] Lock/Unlock buttons show error messages
- [ ] Toast messages appear correctly
- [ ] App handles errors gracefully
- [ ] UI updates properly

---

**🎯 Recommendation**: Use emulator for full testing, physical device for UI/UX testing

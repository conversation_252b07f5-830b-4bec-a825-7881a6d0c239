@echo off
echo.
echo ========================================
echo    EMILocker Build Setup
echo ========================================
echo.

REM Check if Java is available in common locations
set JAVA_FOUND=0

REM Check Android Studio bundled JDK locations
if exist "C:\Program Files\Android\Android Studio\jbr\bin\java.exe" (
    set "JAVA_HOME=C:\Program Files\Android\Android Studio\jbr"
    set JAVA_FOUND=1
    echo ✅ Found Android Studio JBR: %JAVA_HOME%
)

if exist "C:\Program Files\Android\Android Studio\jre\bin\java.exe" (
    set "JAVA_HOME=C:\Program Files\Android\Android Studio\jre"
    set JAVA_FOUND=1
    echo ✅ Found Android Studio JRE: %JAVA_HOME%
)

REM Check user's local Android SDK
if exist "%LOCALAPPDATA%\Android\Sdk\jre\bin\java.exe" (
    set "JAVA_HOME=%LOCALAPPDATA%\Android\Sdk\jre"
    set JAVA_FOUND=1
    echo ✅ Found Android SDK JRE: %JAVA_HOME%
)

REM Check common Java installations
if exist "C:\Program Files\Java\jdk-17\bin\java.exe" (
    set "JAVA_HOME=C:\Program Files\Java\jdk-17"
    set JAVA_FOUND=1
    echo ✅ Found JDK 17: %JAVA_HOME%
)

if exist "C:\Program Files\Java\jdk-11\bin\java.exe" (
    set "JAVA_HOME=C:\Program Files\Java\jdk-11"
    set JAVA_FOUND=1
    echo ✅ Found JDK 11: %JAVA_HOME%
)

if exist "C:\Program Files\Java\jdk1.8.0_*\bin\java.exe" (
    for /d %%i in ("C:\Program Files\Java\jdk1.8.0_*") do (
        set "JAVA_HOME=%%i"
        set JAVA_FOUND=1
        echo ✅ Found JDK 8: %JAVA_HOME%
    )
)

if %JAVA_FOUND%==0 (
    echo.
    echo ❌ Java not found in common locations!
    echo.
    echo Please install Java JDK 8 or higher:
    echo 1. Download from: https://adoptium.net/
    echo 2. Or install via Android Studio: File → Settings → Build Tools → Gradle → Gradle JDK
    echo.
    echo Alternative: Open this project in Android Studio and build from there.
    echo.
    pause
    exit /b 1
)

echo.
echo Setting JAVA_HOME to: %JAVA_HOME%
echo.

REM Add Java to PATH
set "PATH=%JAVA_HOME%\bin;%PATH%"

REM Verify Java is working
echo Testing Java installation...
"%JAVA_HOME%\bin\java.exe" -version
if %errorlevel% neq 0 (
    echo ❌ Java test failed!
    pause
    exit /b 1
)

echo.
echo ✅ Java is working!
echo.

REM Now try to build
echo 🔨 Building EMILocker app...
echo.

gradlew.bat assembleDebug

if %errorlevel% neq 0 (
    echo.
    echo ❌ Build failed!
    echo.
    echo Try these solutions:
    echo 1. Open project in Android Studio and sync
    echo 2. Check if all Android SDK components are installed
    echo 3. Run: gradlew.bat clean assembleDebug
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ Build successful!
echo.

REM Check if APK was created
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✅ APK created: app\build\outputs\apk\debug\app-debug.apk
) else (
    echo ❌ APK not found in expected location
    echo Looking for APK files...
    dir /s *.apk
)

echo.
echo Build complete! You can now install the app.
pause

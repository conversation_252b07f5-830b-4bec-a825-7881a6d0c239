<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@color/background_color">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        tools:context=".MainActivity">

        <!-- App Title -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🔒 EMILocker"
            android:textSize="28sp"
            android:textStyle="bold"
            android:textColor="@color/primary_color"
            android:gravity="center"
            android:layout_marginBottom="12dp" />

        <!-- App Description -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Phone Locking System Based on EMI Payment Status"
            android:textSize="14sp"
            android:textColor="@color/secondary_text_color"
            android:gravity="center"
            android:layout_marginBottom="20dp" />

        <!-- EMI Control Card (Main Toggle) -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="6dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="💳 EMI Payment Control"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_color"
                    android:gravity="center"
                    android:layout_marginBottom="20dp" />

                <!-- EMI Toggle Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@drawable/toggle_background"
                    android:padding="16dp"
                    android:layout_marginBottom="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/emiStatusText"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="EMI Status: NOT PAID ❌"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/primary_text_color" />

                        <Switch
                            android:id="@+id/emiSwitch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="false"
                            android:scaleX="1.8"
                            android:scaleY="1.8"
                            android:layout_marginStart="16dp"
                            android:thumbTint="@color/switch_thumb_color"
                            android:trackTint="@color/switch_track_color" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Toggle to simulate EMI payment status"
                        android:textSize="12sp"
                        android:textColor="@color/secondary_text_color"
                        android:layout_marginTop="8dp"
                        android:gravity="center" />

                </LinearLayout>

                <!-- Advanced Security Controls -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🛡️ Advanced Security Controls"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_color"
                    android:gravity="center"
                    android:layout_marginBottom="16dp" />

                <!-- Camera Block Toggle -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="@drawable/toggle_background"
                    android:padding="12dp"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:id="@+id/cameraStatusText"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="📷 Camera: ALLOWED ✅"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_text_color" />

                    <Switch
                        android:id="@+id/cameraBlockSwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false"
                        android:scaleX="1.3"
                        android:scaleY="1.3"
                        android:layout_marginStart="12dp" />

                </LinearLayout>

                <!-- Wallpaper Block Toggle -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="@drawable/toggle_background"
                    android:padding="12dp"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:id="@+id/wallpaperStatusText"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="🖼️ Wallpaper: ALLOWED ✅"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_text_color" />

                    <Switch
                        android:id="@+id/wallpaperBlockSwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false"
                        android:scaleX="1.3"
                        android:scaleY="1.3"
                        android:layout_marginStart="12dp" />

                </LinearLayout>

                <!-- Manual Control Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <Button
                        android:id="@+id/lockButton"
                        android:layout_width="0dp"
                        android:layout_height="56dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:text="🔒 LOCK"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:backgroundTint="@color/lock_button_color"
                        android:textColor="@android:color/white" />

                    <Button
                        android:id="@+id/unlockButton"
                        android:layout_width="0dp"
                        android:layout_height="56dp"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:text="🔓 UNLOCK"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:backgroundTint="@color/unlock_button_color"
                        android:textColor="@android:color/white" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- System Status Card with Scrollable Content -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📊 System Status"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_text_color"
                    android:layout_marginBottom="12dp" />

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:scrollbars="vertical">

                    <TextView
                        android:id="@+id/statusText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Checking device owner status..."
                        android:textSize="14sp"
                        android:textColor="@color/secondary_text_color"
                        android:lineSpacingExtra="4dp"
                        android:padding="8dp" />

                </ScrollView>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>

@echo off
echo.
echo ========================================
echo    Fixing Gradle Wrapper
echo ========================================
echo.

echo Downloading gradle-wrapper.jar...
echo.

REM Download gradle-wrapper.jar using PowerShell
powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/gradle/gradle/raw/v8.2.0/gradle/wrapper/gradle-wrapper.jar' -OutFile 'gradle\wrapper\gradle-wrapper.jar'}"

if exist "gradle\wrapper\gradle-wrapper.jar" (
    echo ✅ gradle-wrapper.jar downloaded successfully!
) else (
    echo ❌ Failed to download gradle-wrapper.jar
    echo.
    echo Trying alternative download method...
    curl -L -o "gradle\wrapper\gradle-wrapper.jar" "https://github.com/gradle/gradle/raw/v8.2.0/gradle/wrapper/gradle-wrapper.jar"
    
    if exist "gradle\wrapper\gradle-wrapper.jar" (
        echo ✅ gradle-wrapper.jar downloaded successfully with curl!
    ) else (
        echo ❌ Failed to download gradle-wrapper.jar with both methods
        echo.
        echo Manual solution:
        echo 1. Open Android Studio
        echo 2. Open this project
        echo 3. Let Android Studio sync and download dependencies
        echo 4. Or download manually from: https://github.com/gradle/gradle/raw/v8.2.0/gradle/wrapper/gradle-wrapper.jar
        echo 5. Save it to: gradle\wrapper\gradle-wrapper.jar
        pause
        exit /b 1
    )
)

echo.
echo ✅ Gradle wrapper fixed!
echo.

REM Now try to build
echo 🔨 Attempting to build the app...
echo.

REM Set Java path (from previous script)
if exist "C:\Program Files\Android\Android Studio\jbr\bin\java.exe" (
    set "JAVA_HOME=C:\Program Files\Android\Android Studio\jbr"
    set "PATH=%JAVA_HOME%\bin;%PATH%"
)

gradlew.bat assembleDebug

if %errorlevel% neq 0 (
    echo.
    echo ❌ Build still failed. This might be due to:
    echo 1. Missing Android SDK components
    echo 2. Project needs to be synced in Android Studio first
    echo 3. Dependencies need to be downloaded
    echo.
    echo Recommended: Open project in Android Studio and sync first.
    pause
    exit /b 1
)

echo.
echo ✅ Build successful!
echo.
pause
